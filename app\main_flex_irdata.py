# import configparser
# import re
import os
import json

config_string:str = ''
config:dict = {}

current_directory:str = os.path.dirname(__file__)
file_path:str = os.path.join(current_directory, 'volume/TempIRdata.txt')
# print('TempIRdata.txt file_path:' + file_path)
with open(file_path) as ins:
    config_string = ins.read()
    config_string_to_convert = config_string
    config_string_to_convert = config_string_to_convert.replace('\n', '')
    config_string_to_convert = config_string_to_convert.replace('\r', '')
    config_string_to_convert = config_string_to_convert.replace('\t', '')
    config_string_to_convert = config_string_to_convert.replace('{', ',')
    config_string_to_convert = config_string_to_convert.replace('}', '},')
    config_string_to_convert = config_string_to_convert.replace('[', '{\"name\":\"')
    config_string_to_convert = config_string_to_convert.replace(']', '\"')
    config_string_to_convert = config_string_to_convert.rstrip(',')
    config_string_to_convert = '[' + config_string_to_convert + ']'
    config_string_to_convert = config_string_to_convert.rstrip()
    config_array: list = json.loads(config_string_to_convert)
    for json_object in config_array:
        value_dict:dict = {}
        value_dict['frequency'] = json_object['frequency']
        value_dict['irCode'] = json_object['irCode']
        value_dict['preamble'] = json_object['preamble']
        value_dict['repeat'] = json_object['repeat']
        config[json_object['name']] = json.dumps(value_dict.copy())

