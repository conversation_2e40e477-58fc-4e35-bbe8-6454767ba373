#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

import time
import paramiko # type: ignore


class Class():

    def __init__(self, ip:str, port:int, username:str, password:str):
        self.values:dict[str, str] = {'exit': '0x01000000', 'ok': '0x01000004', 'tv': '0x01000039', 'menu': '0x0100003a', '0': '48', '1': '49', '2': '50', '3': '51', '4': '52', '5': '53', '6': '54', '7': '55', '8': '56', '9': '57', 'left': '0x01000012', 'right': '0x01000014', 'up': '0x01000013', 'down': '0x01000015', 'red': '0x01000030',
                       'green': '0x01000031', 'yellow': '0x01000032', 'blue': '0x01000033', 'EPG': '0x01000037', 'info': '89 -a', 'ch+': '0x01000001', 'ch-': '0x01000002', 'power': '85 -a', 'Vol-': '0x01000070', 'Vol+': '0x01000072'}
        self.p = paramiko.SSHClient()
        self.p.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.p.connect(ip, port, username, password, allow_agent=False, look_for_keys=False)

    def main(self, command:str, argsdict):
        json_data:dict = {}
        if not command == '':
            self.p.exec_command('/usr/local/share/app/bin/sendqtevent -kqt ' + self.values[command])
        else:
            for item in argsdict:
                if isinstance(item, float):
                    time.sleep(item)
                elif isinstance(item, str):
                    self.p.exec_command('/usr/local/share/app/bin/sendqtevent -kqt ' + self.values[item])
                else:
                    raise ValueError('Error: ' + str(item) + ' neither string nor float')
        self.p.close()
        json_data['status'] = "success"
        return json_data
