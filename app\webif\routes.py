import json
import os
from os.path import abspath, dirname, join
from typing import Annotated
from fastapi import File
from ..main_dict import devices_dict
from ..main_broadlink_irdata import broadlink_irdata
from ..main_flex_irdata import config, config_string
from fastapi import APIRouter
from ..log_conf import logs_target as logs_target_string

base_dir:str = abspath(dirname(__file__))

webif_router: APIRouter = APIRouter()

@webif_router.get('/')

@webif_router.get('/devices/rename')
def devices_rename(old_device_name:str, new_device_name:str) ->dict:
    new_device_name = new_device_name.translate({ord(c): "_" for c in "!@#$%^&*()[]{};:,./<>?|`~=+"})
    device_exists:bool = check_if_name_exists(new_device_name)
    if device_exists:
        return {
            'status': 'error',
            'message': 'Error with renaming device to ' + new_device_name + ': A device with that name already exists.',
            'message_type': 'negative',
            'message_timeout': 2500
        }
    else:
        for device in devices_dict:
            if device['name'] == old_device_name:
                device['name'] = new_device_name
        save_devices_dict()
        return {
            'status': 'success',
            'devices': devices_dict,
            'message': 'Device \'' + old_device_name + '\' renamed to \'' + new_device_name + '\'',
            'message_type': 'positive',
            'message_timeout': 2500
        }

@webif_router.get('/devices/edit_device')
def devices_edit_device(name:str, params:str) ->dict:
    aDict:dict = json.loads(params)

    for device in devices_dict:
        if device['name'] == name:
            for key, value in aDict.items():
                if key == 'volume_max' or key == 'mag_port' or key == 'adapter_port' or key == 'lms_port' or key == 'irport':
                    device[key] = int(value)
                else:
                    device[key] = value
            break
    save_devices_dict()
    return {
        'status': 'success',
        'devices': devices_dict
    }

@webif_router.get('/devices/delete')
def devices_delete(device_name:str) ->dict:
    device_exists:bool = check_if_name_exists(device_name)
    print('device_exists:' + str(device_exists))
    if not device_exists:
        return {
            'status': 'error',
            'message': 'Error with deleting device ' + device_name + ': A device with that name does not exist.',
            'message_type': 'negative',
            'message_timeout': 2500
        }
    else:
        devices_dict[:] = [d for d in devices_dict if d.get('name') != device_name]
        if save_devices_dict():
            return {
                'status': 'success',
                'devices': devices_dict,
                'message': 'Device \'' + device_name + '\' deleted',
                'message_type': 'positive',
                'message_timeout': 2500
            }
        else:
            return {
                'status': 'error',
                'devices': devices_dict,
                'message': 'Error saving devices',
                'message_type': 'negative',
                'message_timeout': 2500
            }

@webif_router.get('/devices/add')
def devices_add(device_type:str, device_name:str) ->dict:
    device_name = device_name.translate({ord(c): "_" for c in "!@#$%^&*()[]{};:,./<>?|`~=+"})
    device_exists:bool = check_if_name_exists(device_name)
    if device_name == '':
        return {
            'status': 'error',
            'message': 'Error with adding device. The name is empty.',
            'message_type': 'negative',
            'message_timeout': 3500
        }
    if device_exists:
        return {
            'status': 'error',
            'message': 'Error with adding device ' + device_name + ': A device with that name already exists.',
            'message_type': 'negative',
            'message_timeout': 3500
        }
    
    devices_dict.append(type_dict(device_type, device_name)) # type: ignore
    save_devices_dict()
    return {
        'status': 'success',
        'devices': devices_dict,
        'message': 'Device \'' + device_name + '\' Added!',
        'message_type': 'positive',
        'message_timeout': 2500
    }


@webif_router.get('/devices')
def devices() ->dict:
    updated_devices_dict:dict = devices_dict
    return {
        'status': 'success',
        'devices': updated_devices_dict
    }


scripts_file_path:str = join(dirname(base_dir), "volume", "scripts.py")

@webif_router.post('/savescripts')
def savescripts(file: Annotated[bytes, File()]):
    with open(scripts_file_path, 'wb') as fw:
        fw.write(file)
    return {
        'status': 'success'
    }

@webif_router.get('/getscripts')
def getscripts() ->dict:
    with open(scripts_file_path) as file:
        scripts_contents:str = file.read()
    return {
        'status': 'success',
        'scripts_contents': scripts_contents
    }

@webif_router.get('/getlog')
def getlog() ->dict:
    with open(logs_target_string) as file:
        log_contents:str = file.read()
    return {
        'status': 'success',
        'log': log_contents
    }

@webif_router.get('/broadlink_irdata')
def broadlink_irdata_def() ->dict:
    updated_broadlink_irdata:dict = broadlink_irdata
    return {
        'status': 'success',
        'broadlink_irdata': updated_broadlink_irdata
    }

@webif_router.get('/NewTempIRdataBroadLink')
def NewTempIRdataBroadLink(NewTempIRdata:str) ->dict:
    global broadlink_irdata
    broadlink_irdata = json.loads(NewTempIRdata)
    save_broadlink_irdata()
    return {
        'status': 'success',
        'message': 'Broadlink IRData Saved',
        'message_type': 'positive',
        'message_timeout': 2500
    }

@webif_router.get('/NewTempIRdataflex')
def NewTempIRdataflex(NewTempIRdata:str) ->dict:
    global config_string
    config_string = NewTempIRdata
    save_flex_irdata()
    config_string_to_convert = config_string
    config_string_to_convert = config_string_to_convert.replace('\\n', '')
    config_string_to_convert = config_string_to_convert.replace('{', ',')
    config_string_to_convert = config_string_to_convert.replace('}', '},')
    config_string_to_convert = config_string_to_convert.replace('[', '{\"name\":\"')
    config_string_to_convert = config_string_to_convert.replace(']', '\"')
    config_string_to_convert = config_string_to_convert.rstrip(',')
    config_string_to_convert = '[' + config_string_to_convert + ']'
    config_string_to_convert = config_string_to_convert.rstrip()
    global config
    config = {}
    config_array = json.loads(config_string_to_convert)
    for json_object in config_array:
        value_dict = {}
        value_dict['frequency'] = json_object['frequency']
        value_dict['irCode'] = json_object['irCode']
        value_dict['preamble'] = json_object['preamble']
        value_dict['repeat'] = json_object['repeat']
        config[json_object['name']] = json.dumps(value_dict.copy())
    return {
        'status': 'success',
        'message': 'Flex IRData Saved',
        'message_type': 'positive',
        'message_timeout': 2500
    }

@webif_router.get('/flex_irdata')
def flex_irdata_def() ->dict:
    return {
        'status': 'success',
        'flex_irdata': config_string
    }

def save_flex_irdata() ->None:
    current_directory:str = os.path.dirname(__file__)
    parent_directory:str = os.path.split(current_directory)[0]
    file_path:str = os.path.join(parent_directory, 'volume/TempIRdata.txt')
    with open(file_path, 'w+', encoding='utf8') as outfile:
        outfile.writelines(config_string.replace('\\n', '\n'))

def save_broadlink_irdata() ->None:
    current_directory:str = os.path.dirname(__file__)
    parent_directory:str = os.path.split(current_directory)[0]
    file_path:str = os.path.join(parent_directory, 'volume/Commands.json')
    with open(file_path, 'w', encoding='utf8') as outfile:
        json.dump(broadlink_irdata, outfile, indent=4)


def check_if_name_exists(device_name) ->bool:
    for device in devices_dict:
        if device['name'] == device_name:
            return True
    return False


def save_devices_dict() ->bool:
    current_directory:str = os.path.dirname(__file__)
    parent_directory:str = os.path.split(current_directory)[0]
    file_path = os.path.join(parent_directory, 'volume/devices.json')
    with open(file_path, 'w', encoding='utf8') as outfile:
        json.dump(devices_dict, outfile, indent=4)
    return True

def type_dict(device_type, device_name) ->dict:
    if device_type == 'SmartplugTplinkHS':
        return {
            "name": device_name,
            "type": "SmartplugTplinkHS",
            "ip": ""
        }
    if device_type == 'VideoPlayer_Kodi' or device_type == 'VideoPlayer_Egreat' or device_type == 'VideoPlayer_Dune':
        return {
            "name": device_name,
            "type": device_type,
            "ip": ""
        }
    if device_type == 'AudioPlayer_Squeezebox':
        return {
            "name": device_name,
            "type": "AudioPlayer_Squeezebox",
            "mac": "",
            "lms_ip": "",
            "lms_port": "9002"
        }
    if device_type == 'AVR_denon_RS232':
        return {
            "name": device_name,
            "type": "AVR_denon_RS232",
            "volume_max": 80,
            "adapter_ip": "",
            "adapter_port": 0
        }
    if device_type == 'AVR_musiccast':
        return {
            "name": device_name,
            "type": "AVR_musiccast",
            "ip": "",
            "volume_max": 161
        }
    if device_type == 'AVR_MarantzDenon':
        return {
            "name": device_name,
            "type": "AVR_MarantzDenon",
            "ip": "",
            "volume_max": 0
        }
    if device_type == 'Flex':
        return {
            "name": device_name,
            "type": "Flex",
            "ip": "",
            "irport": 0
        }
    if device_type == 'Broadlink':
        return {
            "name": device_name,
            "type": "Broadlink",
            "ip": ""
        }
    if device_type == 'WLED':
        return {
            "name": device_name,
            "type": "WLED",
            "ip": ""
        }
    if device_type == 'AppleTV':
        return {
            "name": device_name,
            "type": "AppleTV",
            "identifier": ""
        }
    if device_type == 'Enigma':
        return {
            "name": device_name,
            "type": "Enigma",
            "ip": ""
        }
    if device_type == 'TV_lg':
        return {
            "name": device_name,
            "type": "TV_lg",
            "ip": "",
            "on_with_MAC": "",
            "on_with_IR_device": "",
            "on_with_IR_command": "",
            "lgtv_code": "",
            "volume_max": 100
        }
    if device_type == 'TV_lg_RS232':
        return {
            "name": device_name,
            "type": "TV_lg_RS232",
            "volume_max": 64,
            "adapter_ip": "",
            "adapter_port": 0
        }
    if device_type == 'HueBridge':
        return {
            "name": device_name,
            "type": "HueBridge",
            "ip": "",
            "username": ""
        }
    if device_type == 'Cololight':
        return {
            "name": device_name,
            "type": "Cololight",
            "ip": ""
        }
    if device_type == 'InfomirMAG':
        return {
            "name": device_name,
            "type": "InfomirMAG",
            "ip": "",
            "mag_port": 22,
            "mag_username": "root",
            "mag_password": "930920"
        }
    if device_type == 'SmartplugTplinkTapo':
        return {
            "name": device_name,
            "type": "SmartplugTplinkTapo",
            "ip": "",
            "tplink_email": "",
            "tplink_password": ""
        }
    if device_type == 'TV_Exlink':
        return {
            "name": device_name,
            "type": "TV_Exlink",
            "volume_max": 100,
            "adapter_ip": "",
            "adapter_port": 80
        }
    return {'error': "Type not found"}

