#!/usr/bin/env python

import socket
from json import loads
# import time


# response_data:dict = {}
commands:dict[str, str] = {'info': '{"system":{"get_sysinfo":{}}}',
            'relay_state': '{"system":{"get_sysinfo":{}}}',
            'on': '{"system":{"set_relay_state":{"state":1}}}',
            'off': '{"system":{"set_relay_state":{"state":0}}}',
            'cloudinfo': '{"cnCloud":{"get_info":{}}}',
            'wlanscan': '{"netif":{"get_scaninfo":{"refresh":0}}}',
                        'time': '{"time":{"get_time":{}}}',
                        'schedule': '{"schedule":{"get_rules":{}}}',
                        'countdown': '{"count_down":{"get_rules":{}}}',
                        'antitheft': '{"anti_theft":{"get_rules":{}}}',
                        'reboot': '{"system":{"reboot":{"delay":1}}}',
                        'reset': '{"system":{"reset":{"delay":1}}}',
                        'energy': '{"emeter":{"get_realtime":{}}}'
            }


class Class():

    def __init__(self, ip:str):
        self.conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.conn.settimeout(4.0)
        try:
            self.conn.connect((ip, 9999))
        except socket.error as e:
            raise ValueError("error: %s" % e)

    def sendMessage(self, command:str) ->dict:
        try:
            self.conn.sendall(self.encrypt(commands[command]))
            response:bytes = self.conn.recv(2048)
            return self.decrypt(response[4:])
        except socket.error as e:
            self.conn.close
            raise ValueError("error: %s" % e)

    def encrypt(self, string):
        key = 171
        result = b"\0\0\0" + chr(len(string)).encode('latin-1')
        for i in string.encode('latin-1'):
            a = key ^ i
            key = a
            result += chr(a).encode('latin-1')
        return result

    def decrypt(self, string):
        key = 171
        result = ""
        for i in string:
            a = key ^ i
            key = i
            result += chr(a)
        return result

    def analyze_relay_state(self, response_data_var) ->dict:
        return_data:dict = {}
        if 'Error' in response_data_var:
            return_data['relay_state'] = 'Error'
            return return_data
        else:
            response_data_dict = loads(response_data_var)
            if isinstance(response_data_dict, dict):
                return_data = {}
                try:
                    relay_state_bool = False
                    if response_data_dict['system']['get_sysinfo']['relay_state']:
                        relay_state_bool = True
                    return_data['relay_state'] = relay_state_bool
                except Exception as e:
                    raise ValueError("error: %s" % e)
            else:
                raise ValueError("Return string not a dict")
        return return_data

    def main(self, command:str):
        response_data:dict = {}
        if command == 'on':
            self.sendMessage('on')
            response_data['relay_state'] = True
        if command == 'off':
            self.sendMessage('off')
            response_data['relay_state'] = False
        if command == 'relay_state':
            response_data = self.analyze_relay_state(self.sendMessage('relay_state'))
        self.conn.close
        response_data['status'] = "success"
        return response_data
