#!/usr/bin/env python
import re
import math


def round_up_one_decimal(n) -> int:
        multiplier = 10 ** 1
        fload_with_one_decimal:float = math.ceil(n * multiplier) / multiplier
        return round(fload_with_one_decimal)

def get_string_between(full_string:str, start_string:str, end_string:str):
    return re.findall(start_string + r'(.+?)' + end_string, full_string)[0]

def str2bool(inputstr:str) ->bool:
    return inputstr.lower() in ("yes", "true", "1", "on")

# full_command:http://*************:8000/device/avrZone*zonenumber*/getVolume40Mute
# returnString:{"mute":false,"volume40":29}
# full_command:http://*************:8000/device/avrZone*zonenumber*/volume40down
# returnString:{"mute":false,"volume40":28}
# full_command:http://*************:8000/device/avrZone*zonenumber*/volume40down?param=3
# returnString:{"mute":false,"volume40":25}
# full_command:http://*************:8000/device/avrZone*zonenumber*/toggleMute
# returnString:{"mute":true,"volume40":25}

def getVol40fromVolOrig(volOrig:float, VolumeRange:dict[str, int]) ->int:
    volume_min:int = VolumeRange['min']
    volume_max:int = VolumeRange['max']
    divider:float = (volume_max - volume_min) / (40 * 1.0)
    return round((volOrig - volume_min) / divider)


def getVolOrigfromVol40(vol40:int, VolumeRange:dict[str, int]) ->float:
    volume_min:int = VolumeRange['min']
    volume_max:int = VolumeRange['max']
    multiplier:float = (volume_max - volume_min) / (40 * 1.0)
    return round_up_one_decimal((vol40 * multiplier) + volume_min)

def merge_dicts(input_dicts:list[dict]) ->dict:
    context:dict = {}
    for d in input_dicts:
        if isinstance(d, dict):
            context.update(d)
        else:
            print('not dict:' + d)
    return dict(context)