#!/usr/bin/env python
import time
# import asyncio
from ..modules import popular_commands
from ..modules import enigma
from ..modules import flex
from ..modules import broad_link
from ..modules import hueBridge
from ..modules import squeeze
from ..modules import cololight
from ..modules import magbox
from ..modules import hs100
from ..modules import tapo
from ..modules import wled
from ..modules import lg
from ..modules import Exlink
from ..modules import musiccast
from ..modules import MarantzDenon
from ..modules import DenonRS232
from ..main_dict import devices_dict
from .functions import merge_dicts

def device_func(device_name:str, command:str, param:str, argsdict:dict, argslist:list):
    device_dict:dict
    try:
        device_dict = next(item for item in devices_dict if item["name"] == device_name)
    except StopIteration:
        raise ValueError('device name not found:', device_name)
    Type = device_dict['type']
    adapter_ip:str = ''
    adapter_port:int = 0
    volume_max:int = 0
    ip:str = ''
    username:str = ''
    password:str = ''
    if Type.startswith('VideoPlayer'):
        if command == 'stop':
            ip = device_dict['ip']
            if Type == 'VideoPlayer_Kodi':
                return popular_commands.kodi_stop(ip)
            elif Type == 'VideoPlayer_Egreat':
                return popular_commands.egreat_stop(ip)
            elif Type == 'VideoPlayer_Dune':
                return popular_commands.dune_standby(ip)
        elif command == 'tv_on':
            ip = device_dict['ip']
            if Type == 'VideoPlayer_Kodi':
                return popular_commands.kodi_tv_on(ip)
        elif command == 'tv_standby':
            ip = device_dict['ip']
            if Type == 'VideoPlayer_Kodi':
                return popular_commands.kodi_tv_standby(ip)
        elif command == 'tv_toggle':
            ip = device_dict['ip']
            if Type == 'VideoPlayer_Kodi':
                return popular_commands.kodi_tv_toggle(ip)
        elif command == 'togglepause':
            ip = device_dict['ip']
            if Type == 'VideoPlayer_Kodi':
                return popular_commands.kodi_play_pause(ip)
        elif command == 'seek':
            ip = device_dict['ip']
            if Type == 'VideoPlayer_Kodi':
                return popular_commands.kodi_seek(ip, int(param))
        elif command == 'start_url':
            ip = device_dict['ip']
            if Type == 'VideoPlayer_Kodi':
                return popular_commands.kodi_start_url(ip, param)
    elif Type.startswith('AudioPlayer'):
        if Type == 'AudioPlayer_Squeezebox':
            Squeezebox_MAC_Adrress:str = device_dict['mac']
            LMS_IP_Adrress:str = device_dict['lms_ip']
            lms_port:int = device_dict['lms_port']
            devsqueeze:squeeze.Class = squeeze.Class(Squeezebox_MAC_Adrress, LMS_IP_Adrress, lms_port)
            return devsqueeze.main(command, param)

    if (Type == 'AVR_musiccast'):
        ip = device_dict['ip']
        volume_max = device_dict['volume_max']
        devmusicast:musiccast.Class = musiccast.Class(ip, volume_max)
        return devmusicast.main(command, param)
    elif (Type == 'AVR_MarantzDenon'):
        # ip = device_dict['ip']
        # return asyncio.run(MarantzDenon.main(ip, command, param))
        ip = device_dict['ip']
        volume_max = device_dict['volume_max']
        devMarantzDenon:MarantzDenon.Class = MarantzDenon.Class(ip, volume_max)
        return devMarantzDenon.main(command, param)

    elif (Type == 'AVR_denon_RS232'):
        volume_max = device_dict['volume_max']
        adapter_ip = device_dict['adapter_ip']
        adapter_port = device_dict['adapter_port']
        devDenonRS232:DenonRS232.Class = DenonRS232.Class(volume_max, adapter_ip, adapter_port)
        return devDenonRS232.main(command, param)
    elif Type == 'SmartplugTplinkHS':
        ip = device_dict['ip']
        smartplug:hs100.Class = hs100.Class(ip)
        if command == 'toggle':
            state = smartplug.main('relay_state')['relay_state']
            if state:
                smartplug = hs100.Class(ip)
                return smartplug.main('off')
            else:
                smartplug = hs100.Class(ip)
                return smartplug.main('on')
        if command == 'restart':
            state = smartplug.main('relay_state')['relay_state']
            if state:
                smartplug = hs100.Class(ip)
                smartplug.main('off')
                time.sleep(4)
                smartplug = hs100.Class(ip)
                return smartplug.main('on')
            else:
                smartplug = hs100.Class(ip)
                return smartplug.main('on')
        return smartplug.main(command)
    elif Type == 'Enigma':
        ip = device_dict['ip']
        devenigma:enigma.Class = enigma.Class(ip)
        return devenigma.main(command, param)
    elif Type == 'Cololight':
        ip = device_dict['ip']
        return_dict = cololight.main(ip, command, param)
        return return_dict
    elif Type == 'SmartplugTplinkTapo':
        ip = device_dict['ip']
        email = device_dict['tplink_email']
        password = device_dict['tplink_password']
        devtapo:tapo.Class = tapo.Class(ip, email, password)
        return devtapo.main(command)
    elif Type == 'Flex':
        ip = device_dict['ip']
        irport:int = device_dict['irport']
        devflex:flex.Class = flex.Class(ip, irport)
        return devflex.main(command, argslist)
    elif Type == 'WLED':
        ip = device_dict['ip']
        devwled:wled.Class = wled.Class(ip)
        return devwled.main(command, param, argsdict)
    elif Type == 'Broadlink':
        ip = device_dict['ip']
        devbroad_link:broad_link.Class = broad_link.Class(ip)
        return devbroad_link.main(command)
    elif Type == 'InfomirMAG':
        ip = device_dict['ip']
        mag_port:int = device_dict['mag_port']
        username = device_dict['mag_username']
        password = device_dict['mag_password']
        devmagbox:magbox.Class = magbox.Class(ip, mag_port, username, password)
        return devmagbox.main(command, argsdict)

    elif Type == 'TV_lg':
        ip = device_dict['ip']
        volume_max = device_dict['volume_max']
        code:str = device_dict['lgtv_code']
        on:dict = {}
        if 'on_with_MAC' in device_dict:
            on['mac'] = device_dict['on_with_MAC']
        if 'on_with_IR_device' in device_dict and 'on_with_IR_command' in device_dict:
            on['device'] = device_dict['on_with_IR_device']
            on['command'] = device_dict['on_with_IR_command']
        devlg:lg.Class = lg.Class(ip, on, code, volume_max)
        return devlg.main(command, param)

    elif Type == 'TV_Exlink':
        volume_max = device_dict['volume_max']
        adapter_ip = device_dict['adapter_ip']
        adapter_port = device_dict['adapter_port']
        devExlink:Exlink.Class = Exlink.Class(volume_max, adapter_ip, adapter_port)
        return devExlink.main(command, param)

    elif Type == 'HueBridge':
        ip = device_dict['ip']
        username = device_dict['username']
        Scene_name:str = ''
        Scene_lights:str = ''
        payload:dict[str, object]
        scene_id:str = ''
        bridge:hueBridge.Class = hueBridge.Class(ip, username)
        if command == 'get_lights':
            return bridge.get_lights()
        elif command == 'create_scene':
            Scene_name = argsdict['name']
            Scene_lights = argsdict['lights']
            payload = {'name': Scene_name,
                       'lights': Scene_lights.split(","),
                       'recycle': False}
            return bridge.create_scene(payload)
            # http://************:6543/hueBridge/create_scene?name=Sofita_red_mood&lights=3,7,10,11,12,13,14,15,16
        elif command == 'delete_scene':
            # http://************:6543/hueBridge/delete_scene?scene_id=scene_id
            scene_id = argsdict['scene_id']
            return bridge.delete_scene(scene_id)
            # http://************:6543/hueBridge/create_scene?name=Sofita_red_mood&lights=3,7,10,11,12,13,14,15,16
        elif command == 'light':
            # light?id=3&modelid=LLC011&hexcode=3FFF88
            # light?id=3&modelid=LLC011&on=true
            # light?id=25&modelid=LWA004&bri=147
            Lightid:int  # required
            Lightmodelid:str  # required
            Lighton:bool
            Lightbri:int
            Lightct:int
            Lighteffect:str
            Lightcolormode:str
            Lighttransitiontime:int
            Lighthexcode:str
            Lightcolorgamuttype:str
            # print('lightsargsdict:' + str(argsdict))
            if 'id' in argsdict:
                Lightid = int(argsdict['id'])  # int, required
            if 'modelid' in argsdict:
                Lightmodelid = argsdict['modelid']  # string, required
            if 'on' in argsdict:
                Lighton = argsdict['on']  # bool
            if 'bri' in argsdict:
                Lightbri = int(argsdict['bri'])  # int
            if 'ct' in argsdict:
                Lightct = int(argsdict['ct'])  # int
            if 'effect' in argsdict:
                Lighteffect = argsdict['effect']  # string
            if 'colormode' in argsdict:
                Lightcolormode = argsdict['colormode']  # string
            if 'transitiontime' in argsdict:
                Lighttransitiontime = int(argsdict['transitiontime'])  # int
            if 'hexcode' in argsdict:
                Lighthexcode = argsdict['hexcode']  # string
            if 'colorgamuttype' in argsdict:
                Lightcolorgamuttype = argsdict['colorgamuttype']  # string
            return bridge.main_func_light(Lightid,
                                           Lightmodelid,
                                           Lighton,
                                           Lightbri,
                                           Lightct,
                                           Lighteffect,
                                           Lighttransitiontime,
                                           Lightcolormode,
                                           Lighthexcode,
                                           Lightcolorgamuttype)
        elif command == 'scene':
            return bridge.main_func_scene(param)
        elif command == 'help':
            return bridge.help_func(param)
        elif command == 'scenename':
            scenes_dict = bridge.help_func('scenes')
            for sceneid, scene_value_dict in scenes_dict.items():
                if param == scene_value_dict['name']:
                    scene_id = sceneid
                    break
            return bridge.main_func_scene(scene_id)
        elif command == 'create_scene_replace':
            return_dict1:dict = {}
            Scene_name = argsdict['name']  # string
            Scene_lights = argsdict['lights']  # string
            payload = {'name': Scene_name,
                       'lights': Scene_lights.split(","),
                       'recycle': False}

            scenes_dict = bridge.help_func('scenes')
            for sceneid, scene_value_dict in scenes_dict.items():
                if Scene_name == scene_value_dict['name']:
                    return_dict1 = bridge.delete_scene(sceneid)
                    time.sleep(1)
                    break

            return_dict2:dict = bridge.create_scene(payload)
            return merge_dicts([return_dict1, return_dict2])
            # http://************:6543/hueBridge/create_scene?name=Sofita_red_mood&lights=3,7,10,11,12,13,14,15,16

        # elif Type.startswith('AppleTV'):
        #     identifier = ''
        #     if 'identifier' in device_dict:
        #         identifier = device_dict['identifier']
        #     return appletv.main(identifier, command, param)
    return
