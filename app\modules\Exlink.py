#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

from time import sleep
from ..modules import functions
import socket
# import struct
# import codecs

# 0% is \x08\x22\x01\x00\x00\x00\xD5 and 35% is \x08\x22\x01\x00\x00\x23\xB2
# b'\x03\x0c\xf2'
queries:dict[str, bytes] = {
    "RESPONSE_SUCCESS" : b'\x03\x0C\xF1',
    "RESPONSE_FAILURE" : b'\x03\x0C\xFF',
    "RESPONSE_TIMEOUT" : b'\x03\x0C\xF2',
    "COMMAND_ON" : b'\x08\x22\x00\x00\x00\x02\xD4',
    "COMMAND_STANDBY" : b'\x08\x22\x00\x00\x00\x01\xD5', # xD7 xD6 x2C
    "COMMAND_TOGGLEMUTE" : b'\x08\x22\x02\x00\x00\x00\xD4',
    "COMMAND_SOURCE_HDMI1" : b'\x08\x22\x0a\x00\x05\x00\xC7',
    "COMMAND_SOURCE_HDMI2" : b'\x08\x22\x0a\x00\x05\x01\xC6',
    "COMMAND_SOURCE_HDMI3" : b'\x08\x22\x0a\x00\x05\x02\xC5',
    "COMMAND_SOURCE_HDMI4" : b'\x08\x22\x0a\x00\x05\x03\xC4',
    "QUERY_POWER" : b'\x08\x22\xF0\x00\x00\x00\xE6',
    "QUERY_VOLUME" : b'\x08\x22\xF0\x01\x00\x00\xE5',
    "QUERY_MUTE" : b'\x08\x22\xF0\x02\x00\x00\xE4',
    "QUERY_CHANNEL" : b'\x08\x22\xF0\x03\x00\x00\xE3',
    "QUERY_SOURCE" : b'\x08\x22\xF0\x04\x00\x00\xE2',
    "COMMAND_SOURCE_TV" : b'\x08\x22\x0a\x00\x00\x00\xCC'
}

class Class():
    def __init__(self, volume_max:int, TCP_IP:str, TCP_PORT:int):
        self.volumerange = {}
        self.volumerange['min'] = 0
        self.volumerange['max'] = volume_max
        self.conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        self.conn.settimeout(5.0)
        self.conn.connect((TCP_IP, TCP_PORT))

    def sendMessage(self, msg_bytes:bytes, msg_len:int) -> bytes:
        try:
            self.conn.sendall(msg_bytes)
            # print('sent:' + str(msg_bytes))
            data = bytearray()
            while len(data) < msg_len:
                packet = self.conn.recv(msg_len - len(data))
                if not packet:
                    return bytearray()
                data.extend(packet)
            return data
        except socket.timeout as e:
            print('timeout')
            return queries['RESPONSE_TIMEOUT']
        except socket.error as e:
            self.conn.close()
            print('error')
            raise ValueError("error: %s" % e)

    def getStatus(self) ->dict:
        json_data:dict[str, object] = {}
        response:bytes = self.sendMessage(queries['QUERY_POWER'], 16)
        if response.startswith(queries['RESPONSE_SUCCESS']):
            json_data['power'] = 'On'
        elif response.startswith(queries['RESPONSE_TIMEOUT']):
            json_data['power'] = 'Off'
            json_data['volume40'] = -1
            json_data['mute'] = False
            return json_data
        elif response.startswith(queries['RESPONSE_FAILURE']):
            json_data['power'] = 'StandBy'
            json_data['volume40'] = -1
            json_data['mute'] = False
            return json_data
        else:
            json_data['power'] = 'UNKNOWN'
            return json_data

        response = self.sendMessage(queries['QUERY_MUTE'], 16)
        if not response.startswith(queries['RESPONSE_SUCCESS']):
            raise ValueError(f"error response:{str(response)}")
        mutebyte:int = response[12]
        if mutebyte == 0:
            json_data['mute'] = False
        elif mutebyte == 1:
            json_data['mute'] = True

        response = self.sendMessage(queries['QUERY_SOURCE'], 16)
        if not response.startswith(queries['RESPONSE_SUCCESS']):
            raise ValueError(f"error response:{str(response)}")
        inputbyte:int = response[12]
        inputstring:str = ''
        if inputbyte == 71:
            inputstring = 'HDMI1'
        elif inputbyte == 72:
            inputstring = 'HDMI2'
        elif inputbyte == 73:
            inputstring = 'HDMI3'
        elif inputbyte == 74:
            inputstring = 'HDMI4'
        elif inputbyte == 0:
            inputstring = 'TV'
            response = self.sendMessage(queries['QUERY_CHANNEL'], 16)
            if not response.startswith(queries['RESPONSE_SUCCESS']):
                raise ValueError(f"error response:{str(response)}")
            # print('QUERY_CHANNEL:' + str(response))
            inputbytetv:int = response[12]
            json_data['channel'] = inputbytetv
        else:
            inputstring = str(inputbyte)
        json_data['input'] = inputstring

        response = self.sendMessage(queries['QUERY_VOLUME'], 16)
        if not response.startswith(queries['RESPONSE_SUCCESS']):
            raise ValueError(f"error response:{str(response)}")
        volbyte:int = response[8]
        if volbyte == 1:
            origvolumeint:int = response[12]
            json_data['volume'] = origvolumeint
            json_data['volume40'] = functions.getVol40fromVolOrig(origvolumeint, self.volumerange)
        json_data['status'] = 'success'
        return json_data

    def setPower(self, param) ->dict:
        response:bytes
        json_data:dict[str, object] = self.getStatus()
        if param == 'On':
            if not json_data['power'] == 'On':
                response = self.sendMessage(queries['COMMAND_ON'], 3)
                if response.startswith(queries['RESPONSE_SUCCESS']):
                    sleep(5)
                    json_data = self.getStatus()
                else:
                    raise ValueError(f"error response:{str(response)}")
        if param == 'StandBy':
            if json_data['power'] == 'On':
                response = self.sendMessage(queries['COMMAND_STANDBY'], 3)
                if response.startswith(queries['RESPONSE_SUCCESS']):
                    json_data['power'] = 'StandBy'
                else:
                    raise ValueError(f"error response:{str(response)}")
        return json_data

    def toggleMute(self) ->dict:
        response:bytes
        json_data:dict[str, object] = self.getStatus()
        response = self.sendMessage(queries['COMMAND_TOGGLEMUTE'], 3)
        if not response.startswith(queries['RESPONSE_SUCCESS']):
            raise ValueError(f"error response:{str(response)}")
        if json_data['mute'] == False:
            json_data['mute'] = True
        else:
            json_data['mute'] = False
        return json_data

    def setSource(self, param) ->dict:
        response:bytes
        json_data:dict[str, object] = self.setPower('On')
        if param == 'HDMI1':
            if not json_data['input'] == 'HDMI1':
                response = self.sendMessage(queries['COMMAND_SOURCE_HDMI1'], 3)
                if response.startswith(queries['RESPONSE_SUCCESS']):
                    json_data['input'] = 'HDMI1'
                else:
                    raise ValueError(f"error response:{str(response)}")
        elif param == 'HDMI2':
            if not json_data['input'] == 'HDMI2':
                response = self.sendMessage(queries['COMMAND_SOURCE_HDMI2'], 3)
                if response.startswith(queries['RESPONSE_SUCCESS']):
                    json_data['input'] = 'HDMI2'
                else:
                    raise ValueError(f"error response:{str(response)}")
        elif param == 'HDMI3':
            if not json_data['input'] == 'HDMI3':
                response = self.sendMessage(queries['COMMAND_SOURCE_HDMI3'], 3)
                if response.startswith(queries['RESPONSE_SUCCESS']):
                    json_data['input'] = 'HDMI3'
                else:
                    raise ValueError(f"error response:{str(response)}")
        elif param == 'HDMI4':
            if not json_data['input'] == 'HDMI4':
                response = self.sendMessage(queries['COMMAND_SOURCE_HDMI4'], 3)
                if response.startswith(queries['RESPONSE_SUCCESS']):
                    json_data['input'] = 'HDMI4'
                else:
                    raise ValueError(f"error response:{str(response)}")
        elif param == 'TV':
            if not json_data['input'] == 'TV':
                response = self.sendMessage(queries['COMMAND_SOURCE_TV'], 3)
                if response.startswith(queries['RESPONSE_SUCCESS']):
                    json_data['input'] = 'TV'
                else:
                    raise ValueError(f"error response:{str(response)}")
        else:
            raise ValueError('Accepted inputs: HDMI1, HDMI2, HDMI3, HDMI4, TV')
        return json_data

    def getVolume40Mute(self) ->dict:
        return self.getStatus()

    def checksum_volume(self, orig_to_set:int) ->bytes:
        cmd:list[int] = [0x08, 0x22, 0x01, 0x00, 0x00, int(orig_to_set)]
        cks = (255 - (sum(cmd)) + 1) % 0xff
        cmd.append(cks)
        fullcomm:str = "".join(f"\\x{x:02X}" for x in cmd)
        my_str_as_bytes:bytes = str.encode(fullcomm)
        return my_str_as_bytes.decode('unicode-escape').encode('ISO-8859-1')

    def checksum_channel(self, orig_to_set:int) ->bytes:
        cmd:list[int] = [0x08, 0x22, 0x04, 0x00, 0x00, int(orig_to_set)]
        cks = (255 - (sum(cmd)) + 1) % 0xff
        cmd.append(cks)
        fullcomm:str = "".join(f"\\x{x:02X}" for x in cmd)
        my_str_as_bytes:bytes = str.encode(fullcomm)
        return my_str_as_bytes.decode('unicode-escape').encode('ISO-8859-1')

    def setchannel_withid(self, param:int) ->dict:
        json_data:dict[str, object] = self.setSource('TV')
        response:bytes = self.sendMessage(self.checksum_channel(int(param)), 3)
        if response.startswith(queries['RESPONSE_SUCCESS']):
            json_data['channel'] = int(param)
        else:
            raise ValueError('setchannel_withid not successful')
        return json_data

    def volume40set(self, param:str, isUP:bool) ->dict:
        json_data:dict[str, object] = self.getStatus()
        if json_data['power'] == 'On':
            json_data['mute'] = False
            paramINT:int = 1
            if not param == '':
                paramINT = int(param)
            volume40:int = json_data['volume40'] # type:ignore
            if volume40 > 39:
                json_data['volume40'] = volume40
                return json_data
            if isUP:
                volume40_toSend:int = volume40 + paramINT
            else:
                volume40_toSend = volume40 - paramINT
            volumeorig_toSend:int = round(functions.getVolOrigfromVol40(volume40_toSend, self.volumerange))
            response:bytes = self.sendMessage(self.checksum_volume(volumeorig_toSend), 3)
            if response.startswith(queries['RESPONSE_SUCCESS']):
                json_data['volume40'] = volume40_toSend
                json_data['volume'] = volumeorig_toSend
                json_data['mute'] = False
        return json_data

    def main(self, command:str, param) ->dict:
            json_data:dict[str, object] = {}
            if (command == "getStatus"):
                json_data = self.getStatus()
            elif (command == "setPower"):
                json_data = self.setPower(param) # 'On' 'StandBy'
            elif (command == "toggleMute"):
                json_data = self.toggleMute()
            elif (command == "setSource"):
                json_data = self.setSource(param) # HDMI1, HDMI2, HDMI3, HDMI4
            elif (command == "getVolume40Mute"):
                json_data = self.getVolume40Mute()
            elif (command == "volume40up"):
                json_data = self.volume40set(param, True)
            elif (command == "volume40down"):
                json_data = self.volume40set(param, False)
            elif (command == "setChannelWithID"):
                json_data = self.setchannel_withid(param)
            self.conn.close()
            return json_data
