#!/usr/bin/env python

# import configparser
# import re
# import json
# import os
# import time
import broadlink # type: ignore
from ..main_broadlink_irdata import broadlink_irdata # type: ignore

class Class():

    def __init__(self, ip:str):
        self.ip:str = ip

    def main(self, command:str) ->dict:
        command_dict:dict = next((item for item in broadlink_irdata if item['Name'] == command))
        sendCommandstr:str = command_dict['Code']
        sendCommandbytes:bytes = bytes.fromhex(sendCommandstr)
        device = broadlink.hello(self.ip)
        device.auth()
        device.send_data(sendCommandbytes)
        return {'status': 'success'}
