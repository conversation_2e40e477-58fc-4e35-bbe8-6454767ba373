#!/usr/bin/env python

import requests # type: ignore
import json


class Class():

    def __init__(self, Squeezebox_MAC_Adrress, LMS_IP_Adrress, Port:int):
        self.Squeezebox_MAC_Adrress = Squeezebox_MAC_Adrress
        self.LMS_IP_Adrress = LMS_IP_Adrress
        self.Port:int = Port

    def main(self, Command:str, param:str) -> dict:
        url = 'http://' + self.LMS_IP_Adrress + ':' + str(self.Port) + '/jsonrpc.js'
        headers = {'Content-Type': 'application/json; charset=utf-8'}
        values:dict = {}
        values["method"] = "slim.request"
        values["params"] = ["' + Squeezebox_MAC_Adrress + '", ["power", "1"]]
        values["id"] = 1
        if Command == 'on':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["power", "1"]]
        elif Command == 'off':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["power", "0"]]
        elif Command == 'pause':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["pause"]]
        elif Command == 'togglepower':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["power"]]
        elif Command == 'play':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["play"]]
        elif Command == 'stop':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["stop"]]
        elif Command == 'play_playlist':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["playlist", "play", param]]
        elif Command == 'volup':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["mixer", "volume", '+5']]
        elif Command == 'voldown':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["mixer", "volume", '-5']]
        elif Command == 'tracknext':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["playlist", "index", '+1']]
        elif Command == 'trackprev':
            values["params"] = [self.Squeezebox_MAC_Adrress, ["playlist", "index", '-1']]
        if not (Command == 'status' or Command == 'toggleshuffle' or Command == 'togglerepeat'):
            try:
                requests.post(url, data=json.dumps(values), headers=headers)
            except ConnectionRefusedError:
                raise ValueError('Squeezebox:' + self.Squeezebox_MAC_Adrress + ' ConnectionRefusedError')
            except ConnectionError:
                raise ValueError('Squeezebox:' + self.Squeezebox_MAC_Adrress + ' ConnectionError')
            except requests.exceptions.RequestException:
                raise ValueError('Squeezebox:' + self.Squeezebox_MAC_Adrress + ' RequestException')
            except Exception:
                raise ValueError('Squeezebox:' + self.Squeezebox_MAC_Adrress + ' Exception')

        values["params"] = [self.Squeezebox_MAC_Adrress, ["status", "-"]]
        status_resp = requests.post(url, data=json.dumps(values), headers=headers).json()
        dict_result = status_resp["result"]
        dict_responce:dict = {}
        dict_responce['power'] = dict_result['power']
        dict_responce['mode'] = dict_result['mode']
        int_volume:int = round(dict_result['mixer volume'] / 5)
        dict_responce['volume'] = str(int_volume)
        dict_responce['playlist repeat'] = dict_result['playlist repeat']
        dict_responce['playlist shuffle'] = dict_result['playlist shuffle']

        if Command == 'togglerepeat':
            int_soll = 2
            int_ist = int(dict_result['playlist repeat'])
            if int_ist > 0:
                int_soll = 0
            values["params"] = [self.Squeezebox_MAC_Adrress, ["playlist", "repeat", str(int_soll)]]
            requests.post(url, data=json.dumps(values), headers=headers)
            dict_responce['playlist repeat'] = str(int_soll)

        if Command == 'toggleshuffle':
            int_soll = 1
            int_ist = int(dict_result['playlist shuffle'])
            if int_ist > 0:
                int_soll = 0
            values["params"] = [self.Squeezebox_MAC_Adrress, ["playlist", "shuffle", str(int_soll)]]
            requests.post(url, data=json.dumps(values), headers=headers)
            dict_responce['playlist shuffle'] = str(int_soll)
        dict_responce['status'] = "success"
        return dict_responce
    
    # Commands that use paths to songs or playlists (parameters below) can use relative paths from the root of the Music Library folder to specify songs. For example, if the Music Library is specified as D:\mymusic and you'd like to refer to a song in that folder named foo.mp3 you can specify just foo.mp3 in the command parameter. Likewise, to refer to items in the Saved Playlist folder, you can use a prefix of __playlists/ before the path. For example, to refer to the saved playlist bar.m3u in the Saved Playlists folder, you can specify a path of __playlists/bar.m3u
    # The power command turns the player on or off. Use 0 to turn off, 1 to turn on, ? to query and no parameter to toggle the power state of the player. For remote streaming connections, the command does nothing and the query always returns 1
