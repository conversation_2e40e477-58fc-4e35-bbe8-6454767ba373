from fastapi import FastAPI # type: ignore
from fastapi.middleware.cors import CORSMiddleware # type: ignore

from logging.config import dictConfig
import logging
from .log_conf import logging_schema

dictConfig(logging_schema)
logger = logging.getLogger(__name__)

logger.info("Starting...")

app: FastAPI = FastAPI()

# origins:list[str] = [
#     "http://localhost.tiangolo.com",
#     "https://localhost.tiangolo.com",
#     "http://localhost",
#     "http://localhost:8080",
# ]
origins = ['http://localhost:3000', 'http://127.0.0.1:3000',
           'https://localhost:3000', 'https://127.0.0.1:3000']

# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=['*'],
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
# )

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from app.volume import scripts
from .webif import routes
from .modules import device_route

app.include_router(scripts.scripts_custom_router)
app.include_router(routes.webif_router)
app.include_router(device_route.device_router)

print('10Muse Control Server running...')

@app.get("/healthcheck")
def healthcheck() -> dict[str, str]:
    return {"status": "OK"}

# .venv\scripts\activate
# deactivate

# python -m pip freeze -l > .\app\requirements.txt
# python -m pip install --upgrade --upgrade-strategy eager -r .\app\requirements.txt

# pip install --force-reinstall -v "PyP100==0.0.25"

# # type: ignore

# if __name__ == "__main__":
#     uvicorn.run(app, host="0.0.0.0", port=8000)


