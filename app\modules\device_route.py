from fastapi import APIRouter, Request
from contextlib import contextmanager
from ..main import logger as my_logger
from ..modules.devices import device_func
import sys
import traceback

device_router: APIRouter = APIRouter()

@contextmanager
def handler():
    try:
        yield
    except:
        exception_type, exception_value, trace = sys.exc_info()
        lines = []
        print(f"Exception type: {exception_type} More details in the logs file") # <class 'RuntimeError'>
        lines.append(f"Exception type: {exception_type}")
        lines.append(f"Exception value: {exception_value}")
        trace_string = "".join(traceback.format_tb(trace))
        lines.append(f"Stack trace:\n{trace_string}")
        my_logger.error('\n'.join(lines))

@device_router.get("/device/{device_name}/{command}")
def device(device_name:str, command:str = '', param:str = '', argsdict:dict = {}, argslist: list = [], id:int = 0, modelid:str = '', on:bool = True, bri:int = 0, ct:int = 0, effect:str = '', colormode:str = '', transitiontime:int = 0, hexcode:str = '', colorgamuttype:str = '', name:str = '', lights:str = '', scene_id:str = '') ->dict:
    print('device_name:' + device_name + ' command:' + command)
    # print('Request.path_params:' + request.url.path)
    json_data:dict = {}
    json_data['device_name'] = device_name
    json_data['command'] = command
    json_data['param'] = param
    json_data['argsdict'] = argsdict # type: ignore
    if command == 'light':
        argsdict['id'] = id
        argsdict['modelid'] = modelid
        argsdict['on'] = on
        argsdict['bri'] = bri
        argsdict['ct'] = ct
        argsdict['effect'] = effect
        argsdict['colormode'] = colormode
        argsdict['transitiontime'] = transitiontime
        argsdict['hexcode'] = hexcode
        argsdict['colorgamuttype'] = colorgamuttype
    if command == 'create_scene_replace' or command == 'create_scene':
        argsdict['name'] = name
        argsdict['lights'] = lights
    if command == 'delete_scene':
        argsdict['scene_id'] = scene_id
    with handler():
        json_data = device_func(device_name, command, param, argsdict, argslist)
        return json_data
    return {
        'status': 'error',
        'message': 'Command ' + command + ' on device ' + device_name + ' produced an error. More details in the logs file.'
    }

