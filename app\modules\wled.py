#!/usr/bin/env python

import requests # type:ignore
import json

class Class():

    def __init__(self, ip:str):
        self.ip = ip

    def send_command(self, argsdict:dict) ->dict:
        headers:dict[str, str] = {'content-type': 'application/json'}
        json_data:requests.Response
        if argsdict == {}:
            json_data = requests.get('http://' + self.ip + '/json/state', headers=headers)
        else:
            json_data = requests.post('http://' + self.ip + '/json/state', data=json.dumps(argsdict), headers=headers)
        if json_data.status_code == 200:
            return {'status': 'success'}
        else:
            raise ValueError("Problem with wled command responce. status_code:" + str(json_data.status_code))

    def main(self, command:str, param:str, argsdict:dict = {}) ->dict:
        if command == 'preset' and not param == '':
            argsdict = {"on": True, "ps":int(param)}
        elif command == 'on':
            argsdict = {"on": True}
        elif command == 'off':
            argsdict = {"on": False}
        return self.send_command(argsdict)
