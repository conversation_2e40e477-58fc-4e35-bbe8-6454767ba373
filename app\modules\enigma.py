#!/usr/bin/env python
#0 pylint: disable=relative-beyond-top-level,

import requests # type: ignore
from ..modules import functions
from xml.etree import ElementTree
from time import sleep
from typing import Optional


class Class():

    def __init__(self, ip:str):
        self.ip:str = ip

    def getVolume40Mute(self) ->dict:
        return_dict:dict = {}
        xml_responce:str = requests.get('http://' + self.ip + '/web/vol?set=state').text
        root:ElementTree.Element = ElementTree.fromstring(xml_responce)
        child_element:Optional[ElementTree.Element] = root.find('e2current')
        if child_element is None:
            raise ValueError('child_element is None')
        child:Optional[str] = child_element.text
        if child is None:
            raise ValueError('child is None')
        origvolumeint:int = int(child.strip())
        return_dict['volume40'] = functions.getVol40fromVolOrig(origvolumeint, {"min": 0, "max": 100})

        child_element = root.find('e2ismuted')
        if child_element is None:
            raise ValueError('child_element is None')
        child = child_element.text
        if child is None:
            raise ValueError('child is None')
        return_dict['mute'] = functions.str2bool(child.strip())
        return return_dict

    def toggleMute(self) ->dict:
        return_dict:dict = self.getVolume40Mute()
        requests.get('http://' + self.ip + '/web/vol?set=mute')
        return_dict['mute'] = not return_dict['mute']
        return return_dict

    def getStandBy(self) ->bool:
        xml_responce:str = requests.get('http://' + self.ip + '/web/powerstate').text
        rootElement:ElementTree.Element = ElementTree.fromstring(xml_responce)
        child_element:Optional[ElementTree.Element] = rootElement.find('e2instandby')
        if child_element is None:
            raise ValueError('child_element is None')
        childtext:Optional[str] = child_element.text
        if childtext is None:
            raise ValueError('childtext is None')
        return functions.str2bool(childtext.strip())

    def power(self, standby_command:bool):
        standby_status:bool = self.getStandBy()
        if standby_status:
            if not standby_command:
                requests.get('http://' + self.ip + '/web/powerstate?newstate=4')
                sleep(4)
        else:
            if standby_command:
                requests.get('http://' + self.ip + '/web/powerstate?newstate=5')
        return standby_command

    def getChannelList(self) ->dict:
        channel_data:dict = {}
        e2servicename2:Optional[str] = ''
        xml_responce_services:str = requests.get('http://' + self.ip + '/web/getservices').text
        root_services:ElementTree.Element = ElementTree.fromstring(xml_responce_services)
        for e2service in root_services.findall('e2service'):
            e2serviceelement:Optional[ElementTree.Element] = e2service.find('e2servicename')
            if e2serviceelement is None:
                raise ValueError('e2serviceelement is None')
            e2servicename:Optional[str] = e2serviceelement.text
            if e2servicename == 'Last Scanned':
                e2servicereference:Optional[ElementTree.Element] = e2service.find('e2servicereference')
                if e2servicereference is None:
                    raise ValueError('e2servicereference is None')
                e2servicename2 = e2servicereference.text
                break
        if e2servicename2 is None:
            raise ValueError('e2servicename2 is None')
        xml_responce_channels:str = requests.get('http://' + self.ip + '/web/getservices?sRef=' + e2servicename2).text
        root_channels:ElementTree.Element = ElementTree.fromstring(xml_responce_channels)
        for e2service in root_channels.findall('e2service'):
            e2channelname:Optional[ElementTree.Element] = e2service.find('e2servicename')
            if e2channelname is None:
                raise ValueError('e2channelname is None')
            e2channelnametext:Optional[str] = e2channelname.text

            e2channelreference:Optional[ElementTree.Element] = e2service.find('e2servicereference')
            if e2channelreference is None:
                raise ValueError('e2channelreference is None')
            e2channelreferencetext:Optional[str] = e2channelreference.text
            if e2channelreferencetext is None:
                raise ValueError('e2channelreferencetext is None')
            e2channelreferencetext = e2channelreferencetext.replace(":", "_")
            e2channelreferencetext = e2channelreferencetext[:-1]
            channel_data[e2channelnametext] = e2channelreferencetext
        return channel_data

    def volume40set(self, param:str, isUP:bool) ->dict:
        paramINT:int = 1
        if not param == '':
            paramINT = int(param)
        response_data40:dict = {}
        response_data40['mute'] = False
        volume40:int = self.getVolume40Mute()['volume40']
        if volume40 > 39:
            response_data40['volume40'] = volume40
            return response_data40
        if isUP:
            volume40_toSend:int = volume40 + paramINT
        else:
            volume40_toSend = volume40 - paramINT
        response_data40['volume40'] = volume40_toSend
        volumeorig_toSend:int = round(functions.getVolOrigfromVol40(volume40_toSend, {"min": 0, "max": 100}))
        requests.get('http://' + self.ip + '/web/vol?set=set' + str(volumeorig_toSend))
        return response_data40

    def main(self, command:str, param) ->dict:
        json_data:dict = {}
        standby_bool:bool = True
        if command == 'getStatus':
            standby_bool = self.getStandBy()
            json_data = self.getVolume40Mute()
        elif command == 'setPower':
            if param == 'On':
                standby_bool = self.power(False)
            if param == 'StandBy':
                standby_bool = self.power(True)
        elif command == 'setChannelWithID':
            standby_bool = self.power(False)
            requests.get('http://' + self.ip + '/web/zap?sRef=' + param.replace("_", ":"))
        elif command == 'displayMessage':
            standby_bool = self.power(False)
            requests.get('http://' + self.ip + '/web/message?text=' + param + '&type=1&timeout=6')
        elif command == 'getVolume40Mute':
            json_data = self.getVolume40Mute()
        elif command == 'toggleMute':
            json_data = self.toggleMute()
        elif command == 'volume40up':
            json_data = self.volume40set(param, True)
        elif command == 'volume40down':
            json_data = self.volume40set(param, False)
        elif command == 'getChannelList':
            json_data = self.getChannelList()
            return json_data
        json_data['standby'] = standby_bool
        json_data['status'] = "success"
        return json_data
