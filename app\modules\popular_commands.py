#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

from . import network_functions
import json
import requests # type:ignore


def kodi_play_pause(Kodi_IP_Adrress:str) ->str:
    # full_command = 'http://' + Kodi_IP_Adrress + ':8080/jsonrpc?request={"jsonrpc":"2.0","id":"1","method":"Player.PlayPause","params":{"playerid":1}}'
    url:str = 'http://%s:8080/jsonrpc' % (Kodi_IP_Adrress)
    values:dict = {}
    values["jsonrpc"] = "2.0"
    values["method"] = "Player.PlayPause"
    values["params"] = {"playerid": 1}
    values["id"] = "1"
    headers:dict[str, str] = {"Content-Type": "application/json"}
    requests.post(url, data=json.dumps(values), headers=headers)
    return json.dumps({'Message': 'Play-Pause command sent to Kodi client ' + Kodi_IP_Adrress})

def kodi_start_url(Kodi_IP_Adrress:str, stream_url:str) ->str:
    # full_command = 'http://*************:8080/jsonrpc?request={"jsonrpc":"2.0","id":"1","method":"Player.Open","params":{"item":{"file":"http://s2.nexuscast.com:8052/;"}},"id":1}'
    url:str = 'http://%s:8080/jsonrpc' % (Kodi_IP_Adrress)
    values:dict = {}
    values["jsonrpc"] = "2.0"
    values["method"] = "Player.Open"
    values["params"] = {"item": {"file":stream_url}}
    values["id"] = "1"
    headers:dict[str, str] = {"Content-Type": "application/json"}
    requests.post(url, data=json.dumps(values), headers=headers)
    return json.dumps({'Message': 'Play-Pause command sent to Kodi client ' + Kodi_IP_Adrress})

def kodi_seek(Kodi_IP_Adrress:str, seconds:int) ->str:
    # full_command = 'http://' + Kodi_IP_Adrress + ':8080/jsonrpc?request={"jsonrpc":"2.0","id":"1","method":"Player.PlayPause","params":{"playerid":1}}'
    # {"jsonrpc":"2.0", "method":"Player.Seek", "params": { "playerid":1, "value":{ "seconds": -30 } }, "id":1}
    # value: {"seconds":-30}
    url:str = 'http://%s:8080/jsonrpc' % (Kodi_IP_Adrress)
    values:dict = {}
    values["jsonrpc"] = "2.0"
    values["method"] = "Player.Seek"
    values["params"] = {"playerid": 1, "value": {"seconds": seconds}}
    values["id"] = "1"
    headers:dict[str, str] = {"Content-Type": "application/json"}
    requests.post(url, data=json.dumps(values), headers=headers)
    return json.dumps({'Message': 'Play-Pause command sent to Kodi client ' + Kodi_IP_Adrress})


def kodi_stop(Kodi_IP_Adrress:str) ->dict:
    url:str = 'http://%s:8080/jsonrpc' % (Kodi_IP_Adrress)
    values:dict = {}
    values["jsonrpc"] = "2.0"
    values["method"] = "Player.Stop"
    values["id"] = "1"
    headers:dict[str, str] = {"Content-Type": "application/json"}
    try:
        values["params"] = {"playerid": 0}
        requests.post(url, data=json.dumps(values), headers=headers)
        values["params"] = {"playerid": 1}
        requests.post(url, data=json.dumps(values), headers=headers)
        values["params"] = {"playerid": 2}
        requests.post(url, data=json.dumps(values), headers=headers)
    except ConnectionRefusedError:
        raise ValueError('Kodi Error: IP:' + Kodi_IP_Adrress + ' ConnectionRefusedError')
    except ConnectionError:
        raise ValueError('Kodi Error: IP:' + Kodi_IP_Adrress + ' ConnectionError')
    except requests.exceptions.RequestException:
        raise ValueError('Kodi Error: IP:' + Kodi_IP_Adrress + ' RequestException')
    except Exception:
        raise ValueError('Kodi Error: IP:' + Kodi_IP_Adrress + ' Exception')
    json_data:dict = {}
    json_data['status'] = "success"
    return json_data


def kodi_show_message(Kodi_IP_Adrress:str):
    full_command:str = 'http://' + Kodi_IP_Adrress + ':8080/jsonrpc?request={"jsonrpc":"2.0","id":"1","method":"GUI.ShowNotification","params":{"title": "Eleni, Giorgos", "message": "Can you see this?", "displaytime": 5000}}'
    # network_functions.http_func(full_command, '')
    return full_command


def kodi_tv_on(Kodi_IP_Adrress:str) ->requests.Response:
    # full_command = 'http://' + Kodi_IP_Adrress + ':8080/jsonrpc?request={"jsonrpc":"2.0","method":"Addons.ExecuteAddon","params":{"addonid":"script.json-cec","params":{"command":"activate"}},"id":1}'
    url:str = 'http://%s:8080/jsonrpc' % (Kodi_IP_Adrress)
    values:dict = {}
    values["jsonrpc"] = "2.0"
    values["method"] = "Addons.ExecuteAddon"
    values["params"] = {"addonid": "script.json-cec", "params": {"command": "activate"}}
    values["id"] = "1"
    headers:dict[str, str] = {"Content-Type": "application/json"}
    return requests.post(url, data=json.dumps(values), headers=headers)


def kodi_tv_standby(Kodi_IP_Adrress:str) ->requests.Response:
    # full_command = 'http://' + Kodi_IP_Adrress + ':8080/jsonrpc?request={"jsonrpc":"2.0","method":"Addons.ExecuteAddon","params":{"addonid":"script.json-cec","params":{"command":"standby"}},"id":1}'
    url:str = 'http://%s:8080/jsonrpc' % (Kodi_IP_Adrress)
    values:dict = {}
    values["jsonrpc"] = "2.0"
    values["method"] = "Addons.ExecuteAddon"
    values["params"] = {"addonid": "script.json-cec", "params": {"command": "standby"}}
    values["id"] = "1"
    headers:dict[str, str] = {"Content-Type": "application/json"}
    return requests.post(url, data=json.dumps(values), headers=headers)


def kodi_tv_toggle(Kodi_IP_Adrress:str) ->requests.Response:
    # full_command = 'http://' + Kodi_IP_Adrress + ':8080/jsonrpc?request={"jsonrpc":"2.0","method":"Addons.ExecuteAddon","params":{"addonid":"script.json-cec","params":{"command":"standby"}},"id":1}'
    url:str = 'http://%s:8080/jsonrpc' % (Kodi_IP_Adrress)
    values:dict = {}
    values["jsonrpc"] = "2.0"
    values["method"] = "Addons.ExecuteAddon"
    values["params"] = {"addonid": "script.json-cec", "params": {"command": "toggle"}}
    values["id"] = "1"
    headers:dict[str, str] = {"Content-Type": "application/json"}
    return requests.post(url, data=json.dumps(values), headers=headers)


def dune_standby(Dune_IP_Adrress:str):
    full_command:str = 'http://' + Dune_IP_Adrress + '/cgi-bin/do?cmd=standby'
    return full_command


def dune_stop(Dune_IP_Adrress:str):
    full_command:str = 'http://' + Dune_IP_Adrress + '/cgi-bin/do?cmd=ir_code&ir_code=E619BF00'
    # network_functions.http_func(full_command, '')
    return full_command


def dune_play_pause(Dune_IP_Adrress:str):
    full_command:str = 'http://' + Dune_IP_Adrress + '/cgi-bin/do?cmd=ir_code&ir_code=AA56BF00'
    # network_functions.http_func(full_command, '')
    return full_command


def egreat_stop(IP_Adrress:str):
    network_functions.tcp_func(IP_Adrress, 33080, 'CMD,keycode_media_stop,END')
    return json.dumps({'Response': 'Egreat Player ' + IP_Adrress + ' stopped'})
