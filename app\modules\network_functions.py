#!/usr/bin/python

import socket
# import struct
from wakeonlan import send_magic_packet

def tcp_func(HOST:str, PORT:int, COMMAND:str):
    sock:socket.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(1.0)
    response:bytes = b''
    try:
        sock.connect((HOST, PORT))
        sock.sendall((COMMAND + "\r").encode())
        response = sock.recv(48)
    except socket.error as e:
        sock.close()
        raise ValueError("error: %s" % e)
    sock.close()
    return response.decode()


def udp_func(UDP_IP:str, UDP_PORT:int, COMMAND:bytes):
    sock:socket.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.sendto(COMMAND, (UDP_IP, UDP_PORT))
    sock.close()


def WakeOnLan(macaddress:str):
    macaddress_lower:str = macaddress.lower()
    send_magic_packet(macaddress_lower)
