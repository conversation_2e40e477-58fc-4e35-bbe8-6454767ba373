#!/usr/bin/env python

# import socket
from json import loads
# from PyP100 import PyP100
from tapo import ApiClient # type: ignore
import time
import asyncio

class Class():

    def __init__(self, ip:str, email:str, password:str):
        self.selfip:str = ip
        self.client = ApiClient(email, password)

    async def asyncfunc(self, command:str):
        device = await self.client.p100(self.selfip)
        if command == 'on':
            await device.on()
        elif command == 'off':
            await device.off()
        elif command == 'relay_state':
            device_info = await device.get_device_info()
            return device_info.to_dict()

    def main(self, command:str):
        relay_state_bool:bool = False
        response_data:dict = {}
        response_data_dict:dict = {}
        if command == 'on':
            # self.device.on()
            asyncio.run(self.asyncfunc('on'))
            response_data['relay_state'] = True
        if command == 'off':
            asyncio.run(self.asyncfunc('off'))
            # self.p100.turnOff()
            response_data['relay_state'] = False
        if command == 'toggle':
            response_data_dict = asyncio.run(self.asyncfunc('relay_state'))
            relay_state_bool = response_data_dict['device_on']
            if relay_state_bool:
                asyncio.run(self.asyncfunc('off'))
                response_data['relay_state'] = False
            else:
                asyncio.run(self.asyncfunc('on'))
                response_data['relay_state'] = True
        if command == 'restart':
            response_data_dict = asyncio.run(self.asyncfunc('relay_state'))
            relay_state_bool = response_data_dict['device_on']
            if relay_state_bool:
                asyncio.run(self.asyncfunc('off'))
                time.sleep(4)
                asyncio.run(self.asyncfunc('on'))
                response_data['relay_state'] = True
            else:
                asyncio.run(self.asyncfunc('on'))
                response_data['relay_state'] = True
        if command == 'relay_state':
            response_data_dict = asyncio.run(self.asyncfunc('relay_state'))
            response_data['relay_state'] = response_data_dict['device_on']
        response_data['status'] = "success"
        return response_data
