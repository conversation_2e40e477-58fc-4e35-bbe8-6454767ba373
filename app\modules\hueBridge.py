#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

import requests # type: ignore
from .qhue import Bridge
from .rgb_cie import Converter
from .rgb_cie import GamutA
from .rgb_cie import GamutB
from .rgb_cie import GamutC
import json
# import cgitb
import traceback


class Class():

    def __init__(self, ip:str, username:str):
        # cgitb.enable()
        self.ip:str = ip
        self.username:str = username
        self.bridge = Bridge(ip, username)

    def create_scene(self, payload) ->dict:
        url:str = 'http://' + self.ip + '/api/' + self.username + '/scenes/'
        res:requests.Response = requests.post(url, json=payload)
        r = json.loads(res.text)
        return_data:dict = {}
        for dic in r:
            if type(dic) is dict:
                for key, value in dic.items():
                    if key == 'success':
                        if type(value) is dict:
                            for key, value in value.items():
                                if key == 'id':
                                    return_data['status'] = "success"
                                    return_data['scene created'] = value
        return return_data

    def delete_scene(self, scene_id) ->dict:
        url = 'http://' + self.ip + '/api/' + self.username + '/scenes/' + scene_id
        res:requests.Response = requests.delete(url)
        r = json.loads(res.text)
        return_data:dict = {}
        for dic in r:
            if type(dic) is dict:
                for key, value in dic.items():
                    if key == 'success':
                        return_data['status'] = "success"
                        return_data['scene deleted'] = value
        return return_data

    def RepresentsInt(self, s):
        try:
            int(s)
            return True
        except ValueError:
            raise ValueError("error: not int")

    def main_func_light(self, Lightid,
                        Lightmodelid,
                        Lighton,
                        Lightbri,
                        Lightct,
                        Lighteffect,
                        Lighttransitiontime,
                        Lightcolormode,
                        Lighthexcode,
                        Lightcolorgamuttype):

        light_data = {}
        Lightid_int = int(float(Lightid))
        Lighteffect_str = str(Lighteffect)  # string
        Lightcolormode_str = str(Lightcolormode)  # string
        Lighthexcode_str = str(Lighthexcode)  # string

        # print('Lighton:' + str(Lighton))
        if (str(Lighton).lower() == 'true'):
            # print('ISTRUE')
            light_data['on'] = bool(Lighton)
        elif (str(Lighton).lower() == 'false'):
            # print('ISFALSE')
            Lighton = ''
            light_data['on'] = bool(Lighton)

        if (len(Lighthexcode_str) == 6):
            converter = Converter()
            if Lightcolorgamuttype == 'A':
                converter = Converter(GamutA)
            elif Lightcolorgamuttype == 'B':
                converter = Converter(GamutB)
            elif Lightcolorgamuttype == 'C':
                converter = Converter(GamutC)
            xy_string = converter.hex_to_xy(Lighthexcode_str)
            light_data['xy'] = xy_string

        if (Lighthexcode_str == 'ct'):
            light_data['colormode'] = Lightcolormode_str

        if (self.RepresentsInt(Lightbri)):
            Lightbri_int = int(float(Lightbri))  # int
            if Lightbri_int > 0:
                light_data['bri'] = Lightbri_int

        if (self.RepresentsInt(Lightct)):
            Lightct_int = int(float(Lightct))  # int
            if Lightct_int > 0:
                light_data['ct'] = Lightct_int

        if (Lighteffect_str == 'colorloop' or Lighteffect_str == 'none'):
            light_data['effect'] = Lighteffect_str

        if (Lightcolormode_str == 'ct' or Lightcolormode_str == 'hs' or Lightcolormode_str == 'xy'):
            light_data['colormode'] = Lightcolormode_str

        if (self.RepresentsInt(Lighttransitiontime)):
            Lighttransitiontime_int = int(float(Lighttransitiontime))  # int
            if Lighttransitiontime_int > 0:
                light_data['transitiontime'] = Lighttransitiontime_int

        if (bool(light_data)):
            print("light_data:" + str(light_data))
            self.bridge.lights[Lightid_int].state(**light_data)

        dict_lamp = {}
        dict_lamp[Lightid] = light_data
        return dict_lamp

    def lights_dict(self, data):
        dict_main = {}
        for (attribute, value) in data.items():
            if (attribute.isdigit()):
                dict_lamp = {}
                value_reachable = ''
                value_colormode = ''
                value_colorgamuttype = ''
                # value_name = ''
                # value_type = ''
                # value_modelid = ''
                # value_uniqueid = ''
                # value_on = ''
                
                # value_effect = ''
                # value_xy = ''
                value_bri = 0.0

                # value_hex = ''
                # value_ct = ''
                

                if 'reachable' in value["state"]:
                    value_reachable = value["state"]["reachable"]
                    dict_lamp['reachable'] = value_reachable

                if value_reachable:
                    if 'name' in value:
                        dict_lamp['name'] = value["name"]

                    if 'type' in value:
                        dict_lamp['type'] = value["type"]

                    if 'productname' in value:
                        dict_lamp['productname'] = value["productname"]

                    # if 'modelid' in value:
                    #     dict_lamp['modelid'] = value["modelid"]

                    # if 'uniqueid' in value:
                    #     dict_lamp['uniqueid'] = value["uniqueid"]

                    if 'on' in value["state"]:
                        dict_lamp['on'] = value["state"]["on"]

                    if 'colormode' in value["state"]:
                        value_colormode = value["state"]["colormode"]
                        dict_lamp['colormode'] = value_colormode

                    # if 'effect' in value["state"]:
                    #     dict_lamp['effect'] = value["state"]["effect"]

                    if 'bri' in value["state"]:
                        value_bri = value["state"]["bri"]
                        dict_lamp['bri'] = str(value_bri)
      
                    # if 'hue' in value["state"]:
                    #     dict_lamp['hue'] = value["state"]["hue"]

                    # if 'sat' in value["state"]:
                    #     dict_lamp['sat'] = value["state"]["sat"]

                    if 'ct' in value["state"]:
                        dict_lamp['ct'] = value["state"]["ct"]

                    if 'colorgamuttype' in value["capabilities"]["control"]:
                        value_colorgamuttype = value["capabilities"]["control"]["colorgamuttype"]
                        dict_lamp['colorgamuttype'] = value_colorgamuttype

                    if 'xy' in value["state"]:
                        value_xy = value["state"]["xy"]
                        value_x = value_xy[0]
                        value_y = value_xy[1]
                        converter = Converter()
                        if value_colorgamuttype == 'A':
                            converter = Converter(GamutA)
                        elif value_colorgamuttype == 'B':
                            converter = Converter(GamutB)
                        elif value_colorgamuttype == 'C':
                            converter = Converter(GamutC)
                        value_hex = converter.xy_to_hex(value_x, value_y, bri=value_bri)  # , bri=0.8
                        # dict_lamp['xy'] = str(value_xy)
                        dict_lamp['hex'] = value_hex

                dict_main[attribute] = dict_lamp

        return dict_main

    def get_lights(self):
        data = self.bridge.lights()
        return self.lights_dict(data)

    def main_func_scene(self, scene_id):
        self.bridge.groups[0].action(scene="" + scene_id + "")
        data = self.bridge.scenes(scene_id)
        return self.scene_dict(data['lightstates'])

    def scene_dict(self, data):
        dict_main = {}
        for (attribute, value) in data.items():
            if (attribute.isdigit()):
                dict_lamp = {}
                value_modelid = ''
                value_on = ''
                value_colormode = ''
                value_effect = ''
                value_xy = ''
                value_bri = ''

                if 'modelid' in value:
                    value_modelid = value["modelid"]
                    dict_lamp['modelid'] = value_modelid

                if 'on' in value:
                    value_on = value["on"]
                    dict_lamp['on'] = value_on

                if 'colormode' in value:
                    value_colormode = value["colormode"]
                    dict_lamp['colormode'] = value_colormode

                if 'effect' in value:
                    value_effect = value["effect"]
                    dict_lamp['effect'] = value_effect

                if 'bri' in value:
                    value_bri = value["bri"]
                    dict_lamp['bri'] = value_bri

                if 'xy' in value:
                    value_xy = value["xy"]
                    value_x = value_xy[0]
                    value_y = value_xy[1]
                    converter = Converter()
                    value_hex = converter.xy_to_hex(
                        value_x, value_y)  # , bri=0.8
                    dict_lamp['xy'] = str(value_xy)
                    dict_lamp['hex'] = value_hex

                dict_main[attribute] = dict_lamp

        return dict_main

    def help_func(self, param):
        if (param == 'lights'):
            data = self.bridge.lights()
            return data
        if (param == 'scenes'):
            data = self.bridge.scenes()
            return data
        if (param == 'scenes_v2'):
            data = self.bridge.scenes()
            return self.scenes_dict(data)
        if (param == 'groups'):
            data = self.bridge.groups()
            return data
        if (param == 'rules'):
            data = self.bridge.rules()
            return data

    def scenes_dict(self, data):
        dict_main = {}
        for (scene_code, value) in data.items():
            if (len(scene_code) > 3):
                dict_scene = {}
                value_name = ''
                value_version = 0
                if 'name' in value:
                    value_name = value["name"]
                if 'version' in value:
                    value_version = value["version"]
                if value_version > 1:
                    dict_scene['code'] = scene_code
                    dict_main[value_name] = dict_scene
        return dict_main
