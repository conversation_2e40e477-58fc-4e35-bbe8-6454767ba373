services:
  controlserver:
    container_name: controlserver
    image: pafs17/controlserver:latest
    ports:
      - "8000:8000"
    volumes:
      - controlserver_data:/app/app/volume
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/healthcheck"]
      interval: 30s
      timeout: 3s
      retries: 3
    environment:
      - PUID=1000
      - PGID=100
    restart: unless-stopped
volumes:
  controlserver_data:
