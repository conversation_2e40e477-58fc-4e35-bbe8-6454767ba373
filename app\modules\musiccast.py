#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

# from . import network_functions
from time import sleep
from . import functions
import requests # type: ignore


class Class():

    def __init__(self, ip, volume_max:int):
        self.ip = ip
        self.volumerange = {}
        self.volumerange['min'] = 0
        self.volumerange['max'] = volume_max

    def getStatus(self):
        response_data = {}
        full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/main/getStatus'
        json_data = requests.get(full_command).json()
        response_data['power'] = json_data['power']
        response_data['volume'] = json_data['volume']
        response_data['mute'] = json_data['mute']
        response_data['input'] = json_data['input']
        response_data['volume40'] = functions.getVol40fromVolOrig(response_data['volume'], self.volumerange)
        return response_data

    def setPower(self, param):
        json_data = self.getStatus()
        isON = False
        param_toSend = ''
        if json_data['power'] == 'on':
            isON = True
        if param == 'on' and isON is False:
            param_toSend = 'on'
            json_data['power'] = 'on'
        elif param == 'standby' and isON is True:
            param_toSend = 'standby'
            json_data['power'] = 'standby'
        else:
            return json_data
        if not param_toSend == '':
            full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/main/setPower?power=' + param_toSend
            requests.get(full_command)
            if param_toSend == 'on':
                sleep(5)
                json_data = self.getStatus()
        return json_data

    def getFeatures(self):
        full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/system/getFeatures'
        return requests.get(full_command).json()

    def toggleMute(self) ->dict:
        response_data:dict = self.getVolume40Mute()
        isMute:bool = response_data['mute']
        if isMute:
            full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/main/setMute?enable=false'
            response_data['mute'] = False
        else:
            full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/main/setMute?enable=true'
            response_data['mute'] = True
        requests.get(full_command)
        return response_data

    def setSource(self, param):
        json_data = self.setPower('on')
        if not json_data['input'] == param:
            full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/main/setInput?input=' + param
            requests.get(full_command)
            json_data['input'] = param
        return json_data

    def setFreq(self, param):
        param = param.lstrip("0") + '0'
        json_data = self.setSource('tuner')
        full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/tuner/setFreq?band=fm&tuning=direct&num=' + param
        requests.get(full_command)
        return json_data

    def getVolume40Mute(self) ->dict:
        response_data:dict = {}
        response_data40:dict = {}
        response_data = self.getStatus()
        response_data40['mute'] = response_data['mute']
        response_data40['volume40'] = response_data['volume40']
        return response_data40

    def volume40set(self, param:str, isUP:bool) ->dict:
        paramINT:int = 1
        if not param == '':
            paramINT = int(param)
        response_data40:dict = {}
        response_data40['mute'] = False
        volume40:int = self.getVolume40Mute()['volume40']
        if volume40 > 39:
            response_data40['volume40'] = volume40
            return response_data40
        if isUP:
            volume40_toSend:int = volume40 + paramINT
        else:
            volume40_toSend = volume40 - paramINT
        response_data40['volume40'] = volume40_toSend
        volumeorig_toSend:int = round(functions.getVolOrigfromVol40(volume40_toSend, self.volumerange))
        full_command = 'http://' + self.ip + '/YamahaExtendedControl/v2/main/setVolume?volume=' + str(volumeorig_toSend)
        requests.get(full_command)
        return response_data40

    def main(self, command:str, param):
        json_data:dict = {}
        if (command == "getStatus"):
            json_data = self.getStatus()
        elif (command == "getFeatures"):
            json_data = self.getFeatures()
        elif (command == "setPower"):
            json_data = self.setPower(param.lower())
        elif (command == "toggleMute"):
            json_data = self.toggleMute()
        elif (command == "setSource"):
            json_data = self.setSource(param)
        elif (command == "setFreq"):
            json_data = self.setFreq(param)
        elif (command == "getVolume40Mute"):
            json_data = self.getVolume40Mute()
        elif (command == "volume40up"):
            json_data = self.volume40set(param, True)
        elif (command == "volume40down"):
            json_data = self.volume40set(param, False)
        json_data['status'] = "success"
        return json_data
