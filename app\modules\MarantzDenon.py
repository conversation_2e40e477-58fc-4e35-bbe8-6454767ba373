#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

from time import sleep
from . import functions
import asyncio

class Class():

    def __init__(self, TCP_IP:str, volume_max:int):
        self.volumerange = {}
        self.volumerange['min'] = 0
        self.volumerange['max'] = volume_max
        self.TCP_IP = TCP_IP

    async def sendMessage(self, msg:str) -> str:
        reader, writer = await asyncio.open_connection(self.TCP_IP, 23)

        print(f'Send: {msg!r}')
        writer.write(msg.encode())
        await writer.drain()
        while True:
            data:bytes = await reader.read(135)
            # ['MV085', 'MVMAX 665', '']
            lines:list[str] = data.decode().split("\r")
            writer.close()
            await writer.wait_closed()
            for line in lines:
                # 'Z2ON'
                if __debug__: print(f'line: {line}')
                prefix:str = 'PW'
                if line.startswith(prefix) and msg.startswith(prefix):
                    return line[len(prefix):]
                prefix = 'MV'
                if line.startswith(prefix) and msg.startswith(prefix) and not msg.startswith('MVMAX'):
                    origstring:str = line[len(prefix):]
                    if len(origstring) == 3:
                        origstring = origstring[:2] + '.' + origstring[2:]
                    return origstring
                prefix = 'MU'
                if line.startswith(prefix) and msg.startswith(prefix):
                    return line
                prefix = 'SI'
                if line.startswith(prefix) and msg.startswith(prefix):
                    return line[len(prefix):]
                prefix = 'Z2'
                if line.startswith(prefix) and msg.startswith(prefix):
                    return line[len(prefix):]
                prefix = 'TFAN'
                if line.startswith(prefix) and msg.startswith(prefix):
                    return line[len(prefix):]
                # prefix = 'PW'
                # if line.startswith(prefix) and (msg.startswith(prefix) or msg.startswith('ZM')):
                #     return line[len(prefix):]
                # if __debug__: print(f'line: {line}' + f'lines: {lines}' + f'msg: {msg}')
            return str(lines)
            # raise ValueError("MarantzDenon: No known Lines:" + str(lines))

    def getStatus(self) -> dict:
        response_data:dict = {}
        response_data['power'] = asyncio.run(self.sendMessage('PW?'))
        returnvolumestring:str = asyncio.run(self.sendMessage('MV?'))
        response_data['volume'] = float(returnvolumestring)
        returnmutestring:str = asyncio.run(self.sendMessage('MU?'))
        if returnmutestring[2:] == 'OFF':
            response_data['mute'] = False
        else:
            response_data['mute'] = True
        response_data['input'] = asyncio.run(self.sendMessage('SI?'))
        response_data['volume40'] = functions.getVol40fromVolOrig(response_data['volume'], self.volumerange)

        # returnzone2string:str = asyncio.run(self.sendMessage('Z2?'))
        # response_data['zone2'] = returnzone2string
        # if returnzone2string[2:] == 'ON':
        #     response_data['zone2'] = asyncio.run(self.sendMessage('Z2OFF'))
        return response_data

    def setPower(self, param:str):
        json_data:dict = self.getStatus()
        isON:bool = False
        param_toSend:str = ''
        if json_data['power'] == 'ON':
            isON = True
        if param == 'ON' and isON is False:
            param_toSend = 'ON'
            json_data['power'] = 'ON'
        elif param == 'STANDBY' and isON is True:
            # param_toSend = 'OFF'
            param_toSend = 'STANDBY'
            json_data['power'] = 'STANDBY'
        else:
            return json_data
        if not param_toSend == '':
            asyncio.run(self.sendMessage('PW' + param_toSend))
            if param_toSend == 'ON':
                sleep(2)
                json_data = self.getStatus()
        return json_data

    def toggleMute(self) ->dict:
        response_data:dict = self.getStatus()
        isMute:bool = response_data['mute']
        if isMute:
            asyncio.run(self.sendMessage('MUOFF'))
            response_data['mute'] = False
        else:
            asyncio.run(self.sendMessage('MUON'))
            response_data['mute'] = True
        return response_data

    def setSource(self, param) ->dict:
        json_data:dict = self.setPower('ON')
        if not json_data['input'] == param:
            asyncio.run(self.sendMessage('SI' + param))
            json_data['input'] = param
        return json_data

    def setFreq(self, param:str) ->dict:
        json_data:dict = self.setSource('TUNER')
        tfan:str = asyncio.run(self.sendMessage('TFAN?'))
        if not param == tfan:
            asyncio.run(self.sendMessage('TFAN' + param))
            json_data['tfan'] = param
        return json_data

    def volume40set(self, param:str, isUP:bool) ->dict:
        paramINT:int = 1
        if not param == '':
            paramINT = int(param)
        response_data40:dict = {}
        response_data40 = self.getStatus()
        response_data40['mute'] = False
        volume40:int = response_data40['volume40']
        if volume40 < 1 or not response_data40['power'] == 'ON':
            response_data40['volume40'] = volume40
            return response_data40
        if isUP:
            volume40_toSend:int = volume40 + paramINT
        else:
            volume40_toSend = volume40 - paramINT
        response_data40['volume40'] = volume40_toSend
        volumeorig_toSend:int = int(functions.getVolOrigfromVol40(volume40_toSend, self.volumerange))
        stringtosend:str = f"MV{volumeorig_toSend:02d}"
        asyncio.run(self.sendMessage(stringtosend))
        response_data40['volume'] = stringtosend
        return response_data40

    def main(self, command:str, param) ->dict:
        json_data:dict = {}
        if (command == "getStatus"):
            json_data = self.getStatus()
        elif (command == "setPower"):
            json_data = self.setPower(param.upper())
        elif (command == "toggleMute"):
            json_data = self.toggleMute()
        elif (command == "setSource"):
            json_data = self.setSource(param)
        elif (command == "setFreq"):
            json_data = self.setFreq(param)
        elif (command == "getVolume40Mute"):
            json_data = self.getStatus()
        elif (command == "volume40up"):
            json_data = self.volume40set(param, True)
        elif (command == "volume40down"):
            json_data = self.volume40set(param, False)
        json_data['status'] = "success"
        return json_data
