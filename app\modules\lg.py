#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

from . import functions
from . import network_functions
from pywebostv.connection import WebOSClient # type: ignore
from pywebostv.controls import MediaControl, ApplicationControl, SystemControl, TvControl # type: ignore
from ping3 import ping # type: ignore
import ipaddress
import re
from . import popular_commands
from time import sleep
import requests # type: ignore


class Class():

    def __init__(self, ip:str, powerOn:dict, code:str, volume_max:int):
        self.ip = ip
        self.volumerange:dict = {}
        self.volumerange['min'] = 0
        self.volumerange['max'] = volume_max
        self.powerOn:dict = powerOn
        self.return_only_code:bool = False
        self.store:dict = {}
        if not code == '':
            self.store['client_key'] = code
        self.client:WebOSClient = WebOSClient(ip)
        self.client.sock.settimeout(2)
        self.connnected:bool = False

    def connect(self):
        if not self.connnected:
            self.client.connect()
            for status in self.client.register(self.store):
                if status == WebOSClient.PROMPTED:
                    # **NOTE**: This will not happen if the store contains valid keys.
                    # print("Please accept the connect on the TV!")
                    self.return_only_code = True
                elif status == WebOSClient.REGISTERED:
                    # print("Registration successful!")
                    self.connnected = True
        return self.connnected
    
    def getCode(self) ->dict:
        connected:bool = self.connect()
        if connected:
            return {'Client Key': self.store['client_key']}
        else:
            return {}
        

    def setPower(self, param:str) ->dict:
        json_data:dict = {}
        json_data = self.getStatus()
        if param == 'ON':
            if not json_data['power'] == 'ON':
                on_dict:dict = self.powerOn
                # print("on_dict:" + str(on_dict))
                if 'device' in on_dict and 'command' in on_dict:
                    device:str = on_dict['device']
                    command:str = on_dict['command']
                    if not device == '' and not command == '':
                        requests.get('http://localhost:8000/device/' + device + '/' + command)
                elif 'mac' in on_dict:
                    mac:str = on_dict['mac']
                    if re.match("[0-9a-f]{2}([-:]?)[0-9a-f]{2}(\\1[0-9a-f]{2}){4}$", mac.lower()):
                        network_functions.WakeOnLan(mac)
                elif 'kodi_cec' in on_dict:
                    kodi_cec:str = on_dict['kodi_cec']
                    if ipaddress.ip_address(kodi_cec):
                        popular_commands.kodi_tv_on(kodi_cec)
                sleep(7)
                json_data = self.getStatus()
        else:
            if json_data['power'] == 'ON':
                system:SystemControl = SystemControl(self.client)
                system.power_off()
                json_data['power'] = "STANDBY"
        return json_data

    def getStatus(self) ->dict:
        response:dict = {}
        json_data:dict = {}
        ping_result = ping(self.ip, timeout=3.8)
        if ping_result:
            if not self.connect():
                return self.store
            elif self.return_only_code:
                return {'Client Key': self.store['client_key']}
            else:
                response = MediaControl(self.client).get_volume()
        else:
            json_data['power'] = 'OFF'
            json_data['volume40'] = -1
            return json_data

        if 'volume' in response:
            json_data['volume'] = response['volume']
            json_data['power'] = 'ON'
            json_data['volume40'] = functions.getVol40fromVolOrig(json_data['volume'], self.volumerange)
        if 'muted' in response:
            json_data['mute'] = response['muted']
        if json_data['power'] == 'ON':
            app:ApplicationControl = ApplicationControl(self.client)
            app_id = app.get_current()
            apps = app.list_apps()
            try:
                foreground_app = [x for x in apps if app_id == x["id"]][0]
            except Exception as e:
                raise ValueError(f"error (LG) in checking foreground_app: {e}, app_id: {app_id}")
            json_data['input'] = foreground_app['title']
        return json_data

    def setSource(self, param:str) ->dict:
        json_data:dict = {}
        json_data = self.setPower('ON')
        if json_data['power'] == 'ON':
            if not json_data['input'] == param:
                app:ApplicationControl = ApplicationControl(self.client)
                apps = app.list_apps()
                yt = [x for x in apps if param in x["title"]][0]
                app.launch(yt, block=True)
                if param == 'LiveTV':
                    sleep(2)
                json_data['input'] = param
        return json_data

    def getVolume40Mute(self) ->dict:
        return self.getStatus()

    def toggleMute(self) ->dict:
        json_data:dict = {}
        json_data = self.setPower('ON')
        if json_data['power'] == 'ON':
            newmutestate_bool:bool = not json_data['mute']
            MediaControl(self.client).mute(newmutestate_bool)
            json_data['mute'] = newmutestate_bool
        return json_data

    def channel_list(self) ->dict:
        ch_data:dict = {}
        if self.setPower('ON')['power'] == 'ON':
            tv_control:TvControl = TvControl(self.client)
            channels_array = tv_control.channel_list()['channelList']
            for item in channels_array:
                ch_data[item['channelName']] = item['channelId']
        return ch_data

    def setchannel_withid(self, param:str) ->dict:
        json_data:dict = {}
        json_data = self.setSource('LiveTV')
        if json_data['power'] == 'ON':
            tv_control:TvControl = TvControl(self.client)
            tv_control.set_channel_with_id(param)
        return json_data

    def show_message(self, param:str) ->dict:
        json_data:dict = {}
        json_data = self.setPower('ON')
        if json_data['power'] == 'ON':
            system:SystemControl = SystemControl(self.client)
            system.notify(param)
        return json_data

    def volume40set(self, param:str, isUP:bool) ->dict:
        json_data:dict = {}
        paramINT:int = 1
        if not param == '':
            paramINT = int(param)
        response_data40:dict = {}
        json_data = self.setPower('ON')
        if json_data['power'] == 'ON':
            volume40:int = json_data['volume40']
            mutestate_bool:bool = json_data['mute']
            if mutestate_bool:
                MediaControl(self.client).mute(False)
            response_data40['mute'] = False
            if volume40 > 39:
                response_data40['volume40'] = volume40
                return response_data40
            if isUP:
                volume40_toSend:int = volume40 + paramINT
            else:
                volume40_toSend = volume40 - paramINT
            response_data40['volume40'] = volume40_toSend
            volumeorig_toSend:int = round(functions.getVolOrigfromVol40(volume40_toSend, self.volumerange))
            MediaControl(self.client).set_volume(volumeorig_toSend)
        return response_data40

    def main(self, command:str, param) ->dict:
        self.connnected = False
        json_data:dict = {}
        if (command == "getStatus"):
            json_data = self.getStatus()
        elif (command == "getCode"):
            json_data = self.getCode()
        elif (command == "setPower"):
            json_data = self.setPower(param.upper())
        elif (command == "toggleMute"):
            json_data = self.toggleMute()
        elif (command == "setSource"):
            json_data = self.setSource(param)
        elif (command == "getVolume40Mute"):
            json_data = self.getVolume40Mute()
        elif (command == "volume40up"):
            json_data = self.volume40set(param, True)
        elif (command == "volume40down"):
            json_data = self.volume40set(param, False)
        elif (command == "getChannelList"):
            return self.channel_list()
        elif (command == "setChannelWithID"):
            json_data = self.setchannel_withid(param)
        elif (command == "displayMessage"):
            json_data = self.show_message(param)
        json_data['status'] = "success"
        return json_data
