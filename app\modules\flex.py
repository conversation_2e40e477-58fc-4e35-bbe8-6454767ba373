#!/usr/bin/env python

import requests # type: ignore
import json
import time
from ..main_flex_irdata import config

class Class():

    def __init__(self, ip:str, port:int):
        self.ip:str = ip
        self.port:int = port
        self.sess = requests.Session()
        self.sess.headers.update({'crossDomain': 'true', 'Content-Type': 'application/json'})

    def sendir(self, data:str):
        self.sess.post('http://' + self.ip + '/api/v1/irports/' + str(self.port) + '/sendir', json=json.loads(str(data)))
        return

    def main(self, command:str, argslist: list = []):
        json_data:dict = {}
        if argslist == []:
            self.sendir(config[command])
        else:
            for item in argslist:
                if isinstance(item, float):
                    time.sleep(item)
                elif isinstance(item, str):
                    self.sendir(config[item])
                else:
                    raise ValueError('Error: ' + str(item) + ' neither string nor float')
        self.sess.close()
        json_data['status'] = "success"
        return json_data
