#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

from time import sleep
from ..modules import functions
import socket


class Class():

    def __init__(self, volume_max:int, adapter_ip:str, adapter_port:int):
        self.volumerange:dict = {}
        self.volumerange['min'] = 0
        self.volumerange['max'] = volume_max
        self.conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        self.conn.settimeout(2.0)
        self.conn.connect((adapter_ip, adapter_port))

    def sendMessage(self, msg:str):
        msg += "\r"
        try:
            self.conn.sendall(msg.encode())
            while True:
                response:bytes = self.conn.recv(2048)
                lines:list[str] = response.decode().split("\r")
                for line in lines:
                    prefix:str = 'TFAN'
                    if msg.startswith(prefix):
                        return line[len(prefix):].lower()
                    prefix = 'PW'
                    if line.startswith(prefix) and msg.startswith(prefix):
                        return line[len(prefix):].lower()
                    prefix = 'MV'
                    if line.startswith(prefix) and msg.startswith(prefix):
                        origstring:str = line[len(prefix):]
                        if len(origstring) == 3:
                            origstring = origstring[:2] + '.' + origstring[2:]
                        return round(float(origstring))
                    prefix = 'MU'
                    if line.startswith(prefix) and msg.startswith(prefix):
                        if line[len(prefix):].lower() == 'off':
                            return False
                        else:
                            return True
                    prefix = 'SI'
                    if line.startswith(prefix) and msg.startswith(prefix):
                        return line[len(prefix):]
        except socket.error as e:
            self.conn.close()
            raise ValueError("error: %s" % e)

    def getStatus(self) ->dict:
        response_data:dict = {}
        response_data['power'] = self.sendMessage('PW?').upper()
        response_data['volume'] = self.sendMessage('MV?')
        response_data['mute'] = self.sendMessage('MU?')
        response_data['input'] = self.sendMessage('SI?')
        response_data['volume40'] = functions.getVol40fromVolOrig(response_data['volume'], self.volumerange)
        return response_data

    def setPower(self, param:str) ->dict:
        json_data:dict = self.getStatus()
        isON:bool = False
        param_toSend:str = ''
        if json_data['power'] == 'ON':
            isON = True
        if param == 'ON' and isON is False:
            param_toSend = 'ON'
            json_data['power'] = 'ON'
        elif param == 'STANDBY' and isON is True:
            param_toSend = 'STANDBY'
            json_data['power'] = 'STANDBY'
        else:
            return json_data
        if not param_toSend == '':
            self.sendMessage('PW' + param_toSend)
            if param_toSend == 'ON':
                sleep(8)
                json_data = self.getStatus()
        return json_data

    def toggleMute(self) ->dict:
        response_data:dict = self.getVolume40Mute()
        isMute:bool = response_data['mute']
        if isMute:
            self.sendMessage('MUOFF')
            response_data['mute'] = False
        else:
            self.sendMessage('MUON')
            response_data['mute'] = True
        return response_data

    def setSource(self, param:str) ->dict:
        json_data:dict = self.setPower('ON')
        if not json_data['input'] == param:
            self.sendMessage('SI' + param)
            json_data['input'] = param
        return json_data

    def setFreq(self, param:str) ->dict:
        json_data:dict = self.setSource('TUNER')
        tfan:str = self.sendMessage('TFAN?')
        if not param == tfan:
            self.sendMessage('TFAN' + param)
        return json_data

    def getVolume40Mute(self) ->dict:
        response_data:dict = {}
        origVol:int = self.sendMessage('MV?')
        response_data['mute'] = self.sendMessage('MU?')
        response_data['volume40'] = functions.getVol40fromVolOrig(origVol, self.volumerange)
        return response_data

    def volume40set(self, param:str, isUP:bool) ->dict:
        paramINT:int = 1
        if not param == '':
            paramINT = int(param)
        response_data40:dict = {}
        response_data40['mute'] = False
        volume40:int = self.getVolume40Mute()['volume40']
        if volume40 > 39:
            response_data40['volume40'] = volume40
            return response_data40
        if isUP:
            volume40_toSend:int = volume40 + paramINT
        else:
            volume40_toSend = volume40 - paramINT
        response_data40['volume40'] = volume40_toSend
        volumeorig_toSend:int = round(functions.getVolOrigfromVol40(volume40_toSend, self.volumerange))
        self.sendMessage('MV' + str(volumeorig_toSend))
        return response_data40

    def main(self, command:str, param) ->dict:
        json_data:dict = {}
        if (command == "getStatus"):
            json_data = self.getStatus()
        elif (command == "setPower"):
            json_data = self.setPower(param.upper())
        elif (command == "toggleMute"):
            json_data = self.toggleMute()
        elif (command == "setSource"):
            json_data = self.setSource(param)
        elif (command == "setFreq"):
            json_data = self.setFreq(param)
        elif (command == "getVolume40Mute"):
            json_data = self.getVolume40Mute()
        elif (command == "volume40up"):
            json_data = self.volume40set(param, True)
        elif (command == "volume40down"):
            json_data = self.volume40set(param, False)
        self.conn.close()
        json_data['status'] = "success"
        return json_data
