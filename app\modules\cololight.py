#!/usr/bin/env python
# pylint: disable=relative-beyond-top-level,

from ..modules import network_functions


def main(UDP_IP, comm:str, param:str) ->dict:
    effects:dict[str, str] = {
        "80s_Club": "049a0000",
        "Cherry_Blossom": "04940800",
        "Cocktail_Parade": "05bd0690",
        "Instagrammer": "03bc0190",
        "Pensieve": "04c40600",
        "Savasana": "04970400",
        "Sunrise": "01c10a00",
        "The_Circus": "04810130",
        "Unicorns": "049a0e00",
        "Christmas": "068b0900",
        "Rainbow_Flow": "03810690",
        "Music_Mode": "07bd0990",
    }

    COMMAND:bytes = b''
    COMMAND_PREFIX:str = "535a30300000000000"
    COMMAND_CONFIG:str = "20000000000000000000000000000000000100000000000000000004010301c"
    COMMAND_EFFECT:str = "23000000000000000000000000000000000100000000000000000004010602ff"

    print('paramcolo:' + param)
    response_data:dict = {}
    if comm == 'on':
        COMMAND = bytes.fromhex("{}{}{}".format(COMMAND_PREFIX, COMMAND_CONFIG, "f35"))
    elif comm == 'off':
        COMMAND = bytes.fromhex("{}{}{}".format(COMMAND_PREFIX, COMMAND_CONFIG, "e1e"))
    elif comm == 'bri':
        brightness_prefix:str = "f"
        COMMAND = bytes.fromhex("{}{}{}{:02x}".format(COMMAND_PREFIX, COMMAND_CONFIG, brightness_prefix, int(param),))
    elif comm == 'color':
        colour_prefix:str = "00"
        COMMAND = bytes.fromhex("{}{}{}{}".format(
            COMMAND_PREFIX, COMMAND_EFFECT, colour_prefix, param))
    elif comm == 'effect':
        COMMAND = bytes.fromhex("{}{}{}".format(
            COMMAND_PREFIX, COMMAND_EFFECT, effects[param.replace(' ', '_')],))
    network_functions.udp_func(UDP_IP, 8900, COMMAND)
    response_data['status'] = "success"
    return response_data
