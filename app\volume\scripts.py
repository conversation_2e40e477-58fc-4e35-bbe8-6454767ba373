from ..modules.device_route import device as device
from fastapi import Request, APIRouter
from urllib import parse
from typing import Optional, TypedDict
from ..modules.functions import merge_dicts
# edited!2

scripts_custom_router: APIRouter = APIRouter()

zones_TV_info:dict[int, dict[str, str]] = {
    1: {'zone_name': 'Sofita', 'device_name': 'TVLGOLED', 'device_type': 'TV_lg'},
    2: {'zone_name': 'Krevatara', 'device_name': 'lgtv_krevatara', 'device_type': 'TV_lg'},
    3: {'zone_name': 'Saloni', 'device_name': 'TV_giorgos', 'device_type': 'TV_Exlink'},
    4: {'zone_name': 'Veranta', 'device_name': 'enigma_zone4', 'device_type': 'enigma'},
    5: {'zone_name': '', 'device_name': '', 'device_type': ''},
    6: {'zone_name': '', 'device_name': '', 'device_type': ''},
    7: {'zone_name': '', 'device_name': '', 'device_type': ''},
    8: {'zone_name': '', 'device_name': '', 'device_type': ''},
    9: {'zone_name': '', 'device_name': '', 'device_type': ''}
    }

TV_providers_Type = TypedDict('TV_providers_Type', {'name':str, 'zone_watching':int})
TV_providers:dict[int, TV_providers_Type] = {
    1: {'name': 'COSMOTE', 'zone_watching': 0},
    2: {'name': 'NOVA', 'zone_watching': 0},
    3: {'name': 'ASTRA', 'zone_watching': 0},
    4: {'name': 'IPTV', 'zone_watching': 0}
    }

selected_script:dict[int, int] = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0} # {zonenumber: selected_script}
# selected_script: 1:movies, 2:music, 3:tv, 4:fmtuner, 5:ALL OFF, 6:PLAYSTATION, 7:APPLETV, 8:NITENDO SWITCH

last_two_channels:dict[int, dict[str, dict]] = {1: {'current': {}, 'last': {}}, 2: {'current': {}, 'last': {}}, 3: {'current': {}, 'last': {}}, 4: {'current': {}, 'last': {}}, 5: {'current': {}, 'last': {}}, 6: {'current': {}, 'last': {}}, 7: {'current': {}, 'last': {}}, 8: {'current': {}, 'last': {}}, 9: {'current': {}, 'last': {}}}
# 1st int is the zone number

@scripts_custom_router.get('/')
@scripts_custom_router.get("/general/{command_name}")
def def_general(command_name:str):
    json_data:dict = {}
    value1:dict = {}
    value2:dict = {}
    value3:dict = {}

    if command_name == 'get_sofita_state':
        value3['smartplug_2'] = device('smartplug_2', 'relay_state')['relay_state']
        value2['smartplug_tree'] = device('smartplug_tree', 'relay_state')['relay_state']
        value1 = device('HueBridge1', 'get_lights')
        json_data = merge_dicts([value1, value2, value3])
    elif command_name == 'smartplug_tree_toggle':
        json_data['smartplug_tree'] = device('smartplug_tree', 'toggle')['relay_state']
    elif command_name == 'smartplug_2_toggle':
        json_data['smartplug_2'] = device('smartplug_2', 'toggle')['relay_state']
    elif command_name == 'tapo1_toggle':
        json_data['tapo1'] = device('tapo1', 'toggle')['relay_state']
    elif command_name == 'sofita_scene_bright':
        value1 = device('HueBridge1', 'scenename', 'sofita_bright')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'on')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'preset', '5')
        device('wled2', 'preset', '5')
        device('wled4', 'preset', '8')
    elif command_name == 'sofita_scene_color':
        value1 = device('HueBridge1', 'scenename', 'sofita_color')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'on')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'preset', '8')
        device('wled2', 'preset', '8')
        device('wled4', 'preset', '8')
    elif command_name == 'sofita_scene_romantic':
        value1 = device('HueBridge1', 'scenename', 'sofita_romantic')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'on')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'preset', '6')
        device('wled2', 'preset', '6')
        device('wled4', 'preset', '6')
    elif command_name == 'sofita_scene_ocean':
        value1 = device('HueBridge1', 'scenename', 'sofita_ocean')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'on')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'preset', '3')
        device('wled2', 'preset', '3')
        device('wled4', 'preset', '3')
    elif command_name == 'sofita_scene_dim':
        value1 = device('HueBridge1', 'scenename', 'sofita_dim')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'on')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'preset', '4')
        device('wled2', 'preset', '4')
        device('wled4', 'preset', '8')
    elif command_name == 'sofita_scene_sleep':
        value1 = device('HueBridge1', 'scenename', 'sofita_alloff')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'off')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'off')
        device('wled2', 'off')
        device('wled4', 'off')
    elif command_name == 'sofita_scene_alloff':
        value1 = device('HueBridge1', 'scenename', 'sofita_alloff')
        value2['smartplug_2'] = device('smartplug_2', 'off')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'off')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'off')
        device('wled2', 'off')
        device('wled4', 'off')
    elif command_name == 'sofita_scene_movie':
        value1 = device('HueBridge1', 'scenename', 'sofita_movie')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'on')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'preset', '4')
        device('wled2', 'preset', '4')
        device('wled4', 'preset', '8')
    elif command_name == 'sofita_scene_warm':
        value1 = device('HueBridge1', 'scenename', 'sofita_warm')
        value2['smartplug_2'] = device('smartplug_2', 'on')['relay_state']
        value3['smartplug_tree'] = device('smartplug_tree', 'on')['relay_state']
        json_data = merge_dicts([value1, value2, value3])
        device('wled1', 'preset', '7')
        device('wled2', 'preset', '7')
        device('wled4', 'preset', '8')
    elif command_name == 'Balcony_Movies':
        json_data = device('HueBridge1', 'scenename', 'Balcony_Movies')
        device('wled3', 'preset', '4')
    elif command_name == 'Balcony_BlueX2':
        json_data = device('HueBridge1', 'scenename', 'Balcony_BlueX2')
        device('wled3', 'preset', '3')
    elif command_name == 'Balcony_RedX2':
        json_data = device('HueBridge1', 'scenename', 'Balcony_RedX2')
        device('wled3', 'preset', '6')
    elif command_name == 'Balcony_DimOrange':
        json_data = device('HueBridge1', 'scenename', 'Balcony_DimOrange')
        device('wled3', 'preset', '4')
    elif command_name == 'Balcony_BlueLight':
        json_data = device('HueBridge1', 'scenename', 'Balcony_BlueLight')
        device('wled3', 'preset', '3')
    elif command_name == 'Balcony_Yellow':
        json_data = device('HueBridge1', 'scenename', 'Balcony_Yellow')
        device('wled3', 'preset', '5')
    elif command_name == 'Balcony_PurlpeRed':
        json_data = device('HueBridge1', 'scenename', 'Balcony_PurlpeRed')
        device('wled3', 'preset', '6')
    elif command_name == 'Balcony_AllOff':
        json_data = device('HueBridge1', 'scenename', 'Balcony_AllOff')
        device('wled3', 'off')
    elif command_name == 'get_hue_lights_state':
        json_data = device('HueBridge1', 'get_lights')

    return json_data  # , indent=4, sort_keys=True

@scripts_custom_router.get("/last_tvchannel/{zonenumber}")
def route_last_tvchannel(zonenumber:int) ->dict:
    route_tvchannel(None, zonenumber, True) # type: ignore
    return {'Response': 'Channel Change OK'}

# http://************:6542/tvchannel/*zonenumber*?channel_lg=1_57_40_0_1_1000_1&channel_enigma=1_0_1_3E8_1_1_EEEE0000_0_0_0&NOVA=802
@scripts_custom_router.get("/tvchannel/{zonenumber}")
def route_tvchannel(request: Request, zonenumber:int, switch:bool = False) ->dict:
    global last_two_channels
    # http://*************:8000/tvchannel/*zonenumber*?channel_lg=1_22_9_0_101_1300_8492&channel_enigma=1_0_1_514_65_212C_EEEE0000_0_0_0

    # print('zonenumber:' + str(zonenumber))
    device_name:str = zones_TV_info[zonenumber]['device_name']
    device_type:str = zones_TV_info[zonenumber]['device_type']

    # EXECUTE SCRIPT
    script_already_active:bool = False
    if selected_script[zonenumber] == 3:
        script_already_active = True
    if not script_already_active:
        script(zonenumber, 3)

    argsdict:dict = {}
    channel_enigma:Optional[str] = ''
    channel_lg:Optional[str] = ''
    channel_lg_last:str = ''
    channel_exlink:Optional[str] = ''
    channel_exlink_last:str = ''
    channel_custom_provider_name:str = ''
    channel_custom_provider_number:int = 0
    channel_custom_channel:str = ''

    opt_provider:str = ''
    opt_provider_number:int = 0
    opt_param:str = ''
    # last_two_channels:dict[int, dict[str, dict]] = {1: {'current': {}, 'last': {}}, 2:
    # KEEP LAST LG CHANNEL SO WE DON'T CHANGE IT IF ITS THE SAME AND ONLY PROVIDER CHANNEL IS CHANGING
    if 'channel_lg' in last_two_channels[zonenumber]['current']:
        channel_lg_last = last_two_channels[zonenumber]['current']['channel_lg']
    if 'channel_exlink' in last_two_channels[zonenumber]['current']:
        channel_exlink_last = last_two_channels[zonenumber]['current']['channel_exlink']
    keep_last_for_case_of_exchange:dict = last_two_channels[zonenumber]['last']

    if not switch:  # DON'T SWITCH, NEW CHANNEL
        # http://*************:8000/tvchannel/*zonenumber*?channel_lg=1_22_9_0_101_1300_8492&amp;channel_enigma=1_0_1_514_65_212C_EEEE0000_0_0_0
        params_dict:dict[str, str] = dict(parse.parse_qsl(parse.urlsplit(str(request.url)).query))
        last_two_channels[zonenumber]['last'] = last_two_channels[zonenumber]['current']
        last_two_channels[zonenumber]['current'] = params_dict
        channel_enigma = params_dict.get('channel_enigma')
        channel_lg = params_dict.get('channel_lg')
        channel_exlink = params_dict.get('channel_exlink')
        for provider_int, provider_dict in TV_providers.items():
            if provider_dict['name'] in params_dict:
                opt_provider = provider_dict['name']
                opt_provider_number = provider_int
                opt_param = str(params_dict[opt_provider])
                channel_custom_provider_number = opt_provider_number
                channel_custom_provider_name = opt_provider
                channel_custom_channel = opt_param
                break
    else:  # SWITCH
        if not script_already_active:  # GO TO TV, LEAVE last_two_channels UNCHANGED
            argsdict = last_two_channels[zonenumber]['current']
        else:  # WE ARE IN TV, EXCHANGE last_two_channels
            argsdict = last_two_channels[zonenumber]['last']
            last_two_channels[zonenumber]['last'] = last_two_channels[zonenumber]['current']
            last_two_channels[zonenumber]['current'] = keep_last_for_case_of_exchange

        if 'channel_enigma' in argsdict:
            channel_enigma = argsdict['channel_enigma']
        if 'channel_lg' in argsdict:
            channel_lg = argsdict['channel_lg']
        if 'channel_exlink' in argsdict:
            channel_exlink = argsdict['channel_exlink']
        for provider_int, provider_dict in TV_providers.items():
            if provider_dict['name'] in argsdict:
                opt_provider = provider_dict['name']
                opt_provider_number = provider_int
                opt_param = str(argsdict[opt_provider])
                channel_custom_provider_number = opt_provider_number
                channel_custom_provider_name = opt_provider
                channel_custom_channel = opt_param
                break

    custom_provider_change_channel(channel_custom_provider_name, channel_custom_channel, channel_custom_provider_number, zonenumber)

    if device_type == 'enigma':
        if not channel_enigma is None and channel_enigma != '':
            device(device_name, 'setChannelWithID', channel_enigma)
    elif device_type == 'TV_lg': # script_already_active
        if not channel_lg is None and channel_lg != '' and (not script_already_active or not (channel_lg == channel_lg_last and (channel_custom_provider_name == 'ASTRA' or channel_custom_provider_name == 'COSMOTE' or channel_custom_provider_name == 'NOVA' or channel_custom_provider_name == 'IPTV'))):
            device(device_name, 'setChannelWithID', channel_lg)
    elif device_type == 'TV_Exlink':
        if not channel_exlink is None and channel_exlink != '' and (not script_already_active or not (channel_exlink == channel_exlink_last and (channel_custom_provider_name == 'ASTRA' or channel_custom_provider_name == 'COSMOTE' or channel_custom_provider_name == 'NOVA' or channel_custom_provider_name == 'IPTV'))):
            device(device_name, 'setChannelWithID', channel_exlink)
    return {'Response': 'Channel change OK'}

def custom_provider_change_channel(channel_custom_provider_name:str, channel_custom_channel:str, channel_custom_provider_number:int, zonenumber:int) ->dict:
    returnmessage:str = ''
    we_had_BBC:bool = False
    # argsdict = {}

    # If we are already at BBC, don't add 'TV' button to the buttons sequence
    # Also stop streaming if we are out
    # This has to come before 'check_provider_availability'
    if int(TV_providers[4]['zone_watching']) == zonenumber:
        we_had_BBC = True

    values_list:list = []
    char:str
    if channel_custom_provider_name == 'ASTRA':
        returnmessage = check_provider_availability(zonenumber, channel_custom_provider_number)
        if returnmessage == 'allow':
            device('enigma_astra', 'setChannelWithID', channel_custom_channel)
    elif channel_custom_provider_name == 'COSMOTE':
        if channel_custom_channel.isdigit():
            returnmessage = check_provider_availability(zonenumber, channel_custom_provider_number)
            if returnmessage == 'allow':
                for char in list(channel_custom_channel):
                    values_list.append('COSMOTETV_' + char)
                    values_list.append(0.12)
                device('flex_NOVA_COSMOTETV', argslist=values_list)
    elif channel_custom_provider_name == 'NOVA':
        if channel_custom_channel.isdigit():
            returnmessage = check_provider_availability(zonenumber, channel_custom_provider_number)
            if returnmessage == 'allow':
                last_char:str = ''
                for char in list(channel_custom_channel):
                    if last_char == char:
                        values_list.append(0.12)
                    values_list.append('NOVANEW_' + char)
                    values_list.append(0.12)
                    last_char = char
                values_list.append('NOVANEW_ARROWOK')
                device('flex_NOVA_COSMOTETV', argslist=values_list)
    elif channel_custom_provider_name == 'IPTV':
        if channel_custom_channel.isdigit():
            returnmessage = check_provider_availability(zonenumber, channel_custom_provider_number)
            if returnmessage == 'allow':
                if not we_had_BBC:
                    values_list.append('tv')
                    values_list.append(0.4)
                for char in list(channel_custom_channel):
                    values_list.append(char)
                    values_list.append(0.12)
                values_list.append('ok')
                device('mag322', argsdict=dict(values_list))
    elif channel_custom_provider_name == '':
        check_provider_availability(zonenumber, 0)
    if we_had_BBC:
        pass
        # stop_streaming()
    return {}

def stop_streaming() ->dict:
    # Stop Mag Box Streaming. It has to be repeated in free_providers_for_zone
    if int(TV_providers[4]['zone_watching']) == 0:
        # noone is watching Mag Box, lets stop streaming
        values_list:list = []
        values_list.append('menu')
        device('mag322', argsdict=dict(values_list))
    return {}

def free_providers_for_zone(zonenumber:int, also_stop_streaming:bool = True) ->dict:
    global TV_providers
    for provider_int, provider_dict in TV_providers.items():
        if int(provider_dict['zone_watching']) == zonenumber:
            TV_providers[provider_int]['zone_watching'] = 0
    if also_stop_streaming:
        pass
        # print("cancel stop_streaming")
        # stop_streaming()
    return {}

def check_provider_availability(zonenumber:int, selected_provider:int) ->str:
    global TV_providers
    device_name = zones_TV_info[zonenumber]['device_name']
    device_type = zones_TV_info[zonenumber]['device_type']
    returnmessage = ''
    if selected_provider == 0:
        free_providers_for_zone(zonenumber, False)
    else:
        for provider_int, provider_dict in TV_providers.items():
            if not provider_int == selected_provider:
                if TV_providers[provider_int]['zone_watching'] == str(zonenumber):
                    TV_providers[provider_int]['zone_watching'] = 0
            else:
                if int(provider_dict['zone_watching']) == zonenumber or int(provider_dict['zone_watching']) == 0:
                    TV_providers[provider_int]['zone_watching'] = zonenumber
                    returnmessage = 'allow'
                else:
                    if not device_type=='channel_exlink':
                        device(device_name, 'displayMessage', zones_TV_info[int(provider_dict['zone_watching'])]['zone_name'] + ' is already connected to ' + TV_providers[provider_int]['name'])
                    returnmessage = 'FORBID'
    return returnmessage

@scripts_custom_router.get("/internetradiourl/{zonenumber}")
def internetradiourl(zonenumber:int, stream_url:str = ''):
    if not selected_script[zonenumber] == 2:
        script(zonenumber, 2)
    json_data:dict = {}
    json_data = device('AudioPlayerZone' + str(zonenumber), 'play_playlist', stream_url)
    return json_data

@scripts_custom_router.get("/script/{zonenumber}/{scriptnumber}")
def script(zonenumber:int, scriptnumber:int, param:str = ''):
    # print('zonenumber:' + str(zonenumber) + ' scriptnumber:' + str(scriptnumber))
    selected_script[zonenumber] = -1
    json_data:dict = {}
    force = False
    if param == 'force':
        force = True
    if zonenumber == 1:
        if scriptnumber == 1:  # MOVIES
            if not device('TVLGOLED', 'setSource', 'HDMI3')['status'] == "success":
                return

            json_data = device('avrZone' + str(zonenumber), 'setSource', 'TV/CBL')
            if not json_data['status'] == "success":
                return

            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return
            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 2:  # MUSIC
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'CD')
            if not json_data['status'] == "success":
                return

            if not param == 'not_music_play':
                if not device('AudioPlayerZone' + str(zonenumber), 'play')['status'] == "success":
                    return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 3:  # TV
            if not device('TVLGOLED', 'setSource', 'LiveTV')['status'] == "success":
                return

            json_data = device('avrZone' + str(zonenumber), 'setSource', 'TV/CBL')
            if not json_data['status'] == "success":
                return

            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 5:  # ALL OFF
            json_data = device('avrZone' + str(zonenumber), 'setPower', 'StandBy')
            if not json_data['status'] == "success":
                return

            if not device('TVLGOLED', 'setPower', 'StandBy')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 6:  # PLAYSTATION
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'TV/CBL')
            if not json_data['status'] == "success":
                return

            if not device('TVLGOLED', 'setSource', 'HDMI4')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 7:  # APPLETV
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'TV/CBL')
            if not json_data['status'] == "success":
                return
            if not device(zones_TV_info[zonenumber]['device_name'], 'setChannelWithID', '1_45_43_0_4_2404_4')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
    if zonenumber == 3:
        if scriptnumber == 1:  # Movies
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'audio1')
            if not json_data['status'] == "success":
                return

            if not device('TV_giorgos', 'setSource', 'HDMI3')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 2:  # Music
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'av3')
            if not json_data['status'] == "success":
                return

            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('TV_giorgos', 'setPower', 'StandBy')['status'] == "success":
                return
            if not param == 'not_music_play':
                if not device('AudioPlayerZone' + str(zonenumber), 'play')['status'] == "success":
                    return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 3:  # TV
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'audio1')
            # print('json_data:' + str(json_data))
            if not json_data['status'] == "success":
                return

            if not device('TV_giorgos', 'setSource', 'HDMI2')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return

            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 6:  # PLAYSTATION
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'audio1')
            if not json_data['status'] == "success":
                return

            if not device('TV_giorgos', 'setSource', 'HDMI1')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 8:  # NITENDO SWITCH
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'audio1')
            if not json_data['status'] == "success":
                return

            if not device('TV_giorgos', 'setSource', 'HDMI4')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 5:  # ALLOFF
            json_data = device('avrZone' + str(zonenumber), 'setPower', 'standby')
            if not json_data['status'] == "success":
                return
            if not device('TV_giorgos', 'setPower', 'StandBy')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('enigma_zone' + str(zonenumber), 'setPower', 'standby')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 7:  # APPLETV
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'audio1')
            if not json_data['status'] == "success":
                return
            if not device(zones_TV_info[zonenumber]['device_name'], 'setChannelWithID', '51')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return
            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
    if zonenumber == 4:
        if scriptnumber == 1:  # MOVIES
            if not device('rmmini1', 'Hisense_TV_ON')['status'] == "success":
                return

            json_data = device('avrZone' + str(zonenumber), 'setSource', 'BD')
            if not json_data['status'] == "success":
                return

            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 2:  # MUSIC
            json_data = device('avrZone' + str(zonenumber), 'setSource', 'AUX1')
            if not json_data['status'] == "success":
                return

            if not device('AudioPlayerZone' + str(zonenumber), 'play')['status'] == "success":
                return

            if not device('rmmini1', 'Hisense_TV_OFF')['status'] == "success":
                return

            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 3:  # TV
            if not device('rmmini1', 'Hisense_TV_ON')['status'] == "success":
                return

            json_data = device('avrZone' + str(zonenumber), 'setSource', 'MPLAY')
            if not json_data['status'] == "success":
                return

            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 5:  # ALL OFF
            if not device('rmmini1', 'Hisense_TV_OFF')['status'] == "success":
                return

            json_data = device('avrZone' + str(zonenumber), 'setPower', 'StandBy')
            if not json_data['status'] == "success":
                return

            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return
            if not device('enigma_zone' + str(zonenumber), 'setPower', 'StandBy')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
        if scriptnumber == 7:  # APPLETV
            if not device('rmmini1', 'Hisense_TV_ON')['status'] == "success":
                return

            json_data = device('avrZone' + str(zonenumber), 'setSource', 'MPLAY')

            if not device(zones_TV_info[zonenumber]['device_name'], 'setChannelWithID', '1_0_1_964_4_4_EEEE0000_0_0_0')['status'] == "success":
                return

            if not device('VideoPlayerZone' + str(zonenumber), 'stop')['status'] == "success":
                return
            if not device('AudioPlayerZone' + str(zonenumber), 'off')['status'] == "success":
                return

            free_providers_for_zone(zonenumber)
            selected_script[zonenumber] = scriptnumber
    return json_data
